import React from 'react';
/**
 * Bundle Optimization & Resource Management
 * Advanced techniques for optimizing bundle size and loading performance
 */

import { log } from './logger'

interface ResourceHint {
  href: string
  as?: string
  type?: string
  crossorigin?: string
}

interface BundleMetrics {
  totalSize: number
  loadTime: number
  cacheHitRate: number
  compressionRatio: number
}

class BundleOptimizer {
  private preloadedResources = new Set<string>()
  private prefetchedResources = new Set<string>()
  private criticalResources = new Set<string>()
  private metrics: BundleMetrics = {
    totalSize: 0,
    loadTime: 0,
    cacheHitRate: 0,
    compressionRatio: 0
  }

  /**
   * Preload critical resources for immediate use
   */
  preloadResource(href: string, as: string, type?: string, crossorigin?: string): void {
    if (this.preloadedResources.has(href)) return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type
    if (crossorigin) link.crossOrigin = crossorigin

    // Add error handling
    link.onerror = () => {
      log.warn('bundle', `Failed to preload resource: ${href}`)
    }

    link.onload = () => {
      log.debug('bundle', `Successfully preloaded: ${href}`)
    }

    document.head.appendChild(link)
    this.preloadedResources.add(href)
    this.criticalResources.add(href)
  }

  /**
   * Prefetch resources for future use
   */
  prefetchResource(href: string): void {
    if (this.prefetchedResources.has(href)) return

    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href

    link.onerror = () => {
      log.warn('bundle', `Failed to prefetch resource: ${href}`)
    }

    document.head.appendChild(link)
    this.prefetchedResources.add(href)
  }

  /**
   * Preconnect to external domains
   */
  preconnectDomain(domain: string, crossorigin = false): void {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    if (crossorigin) link.crossOrigin = 'anonymous'

    document.head.appendChild(link)
    log.debug('bundle', `Preconnected to domain: ${domain}`)
  }

  /**
   * DNS prefetch for external domains
   */
  dnsPrefetch(domain: string): void {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = domain

    document.head.appendChild(link)
    log.debug('bundle', `DNS prefetch for domain: ${domain}`)
  }

  /**
   * Optimize critical resource loading
   */
  optimizeCriticalResources(): void {
    // Preload critical CSS
    this.preloadResource('/assets/critical.css', 'style', 'text/css')
    
    // Preload critical fonts
    this.preloadResource('/assets/fonts/arabic-font.woff2', 'font', 'font/woff2', 'anonymous')
    this.preloadResource('/assets/fonts/english-font.woff2', 'font', 'font/woff2', 'anonymous')

    // Preconnect to API domain
    if (import.meta.env.VITE_API_URL) {
      this.preconnectDomain(import.meta.env.VITE_API_URL, true)
    }

    // DNS prefetch for external services
    this.dnsPrefetch('//fonts.googleapis.com')
    this.dnsPrefetch('//cdn.jsdelivr.net')
  }

  /**
   * Lazy load non-critical resources
   */
  lazyLoadNonCritical(): void {
    // Prefetch route chunks that are likely to be visited
    const likelyRoutes = [
      '/admin/dashboard',
      '/admin/employees',
      '/admin/departments'
    ]

    likelyRoutes.forEach(route => {
      this.prefetchResource(`/assets/chunks/${route.replace(/\//g, '-')}.js`)
    })
  }

  /**
   * Monitor bundle performance
   */
  monitorPerformance(): void {
    if (!('performance' in window)) return

    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach(((entry) => {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          
          // Track bundle metrics
          this.metrics.totalSize += resourceEntry.transferSize || 0
          this.metrics.loadTime = Math.max(this.metrics.loadTime, resourceEntry.duration)

          // Check cache hit rate
          if (resourceEntry.transferSize === 0 && resourceEntry.decodedBodySize > 0) {
            this.metrics.cacheHitRate++
          }

          // Log slow resources
          if (resourceEntry.duration > 1000) {
            log.warn('bundle', `Slow resource detected: ${resourceEntry.name} (${resourceEntry.duration}ms)`)
          }
        }
      })
    })

    observer.observe({ entryTypes: ['resource'] })
  }

  /**
   * Implement service worker for caching
   */
  initializeServiceWorker(): void {
    if ('serviceWorker' in navigator && import.meta.env.PROD) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          log.info('bundle', 'Service Worker registered successfully')
          
          // Update available
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content available
                  this.notifyUpdate()
                }
              })
            }
          })
        })
        .catch((error) => {
          log.error('bundle', 'Service Worker registration failed', error)
        })
    }
  }

  /**
   * Notify user of available updates
   */
  private notifyUpdate(): void {
    if (window.confirm('تحديث جديد متاح. هل تريد تحديث التطبيق؟')) {
      // FIXED: Use refresh event instead of page reload
      window.dispatchEvent(new CustomEvent('app:force-refresh', {
        detail: { timestamp: Date.now(), source: 'bundle-update' }
      }))
    }
  }

  /**
   * Optimize images with modern formats
   */
  optimizeImages(): void {
    // Check for WebP support
    const webpSupported = this.supportsWebP()
    const avifSupported = this.supportsAVIF()

    if (webpSupported || avifSupported) {
      log.info('bundle', `Modern image formats supported: WebP=${webpSupported}, AVIF=${avifSupported}`)
    }
  }

  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
  }

  private supportsAVIF(): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    try {
      return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
    } catch {
      return false
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics(): BundleMetrics {
    return { ...this.metrics }
  }

  /**
   * Initialize all optimizations
   */
  initialize(): void {
    log.info('bundle', 'Initializing bundle optimizations...')
    
    this.optimizeCriticalResources()
    this.lazyLoadNonCritical()
    this.monitorPerformance()
    this.optimizeImages()
    
    // Initialize service worker in production
    if (import.meta.env.PROD) {
      this.initializeServiceWorker()
    }

    log.info('bundle', 'Bundle optimizations initialized')
  }
}

// Singleton instance
export const bundleOptimizer = new BundleOptimizer()

// Utility functions
export const preloadCriticalRoute = (routePath: string) => {
  const chunkName = routePath.replace(/\//g, '-')
  bundleOptimizer.preloadResource(`/assets/chunks/${chunkName}.js`, 'script')
}

export const prefetchRoute = (routePath: string) => {
  const chunkName = routePath.replace(/\//g, '-')
  bundleOptimizer.prefetchResource(`/assets/chunks/${chunkName}.js`)
}

// Initialize on module load
if (typeof window !== 'undefined') {
  // Delay initialization to not block initial render
  setTimeout(() => {
    bundleOptimizer.initialize()
  }, 100)
}

export default bundleOptimizer
