/**
 * FIXED: Performance Optimization Utilities
 * Comprehensive utilities for optimizing React performance and preventing bottlenecks
 */

import React, { useCallback, useRef, useMemo, useEffect, useState } from 'react'

// FIXED: Debounce hook for expensive operations
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// FIXED: Throttle hook for high-frequency events
export const useThrottle = <T extends (...args: any[]) => any,>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

// FIXED: Memoized expensive computation hook
export const useExpensiveComputation = <T, D extends readonly unknown[],>(
  computeFn: () => T,
  deps: D,
  shouldCompute: boolean = true
): T | undefined => {
  return useMemo(() => {
    if (!shouldCompute) return undefined

    const startTime = performance.now()
    const result = computeFn()
    const endTime = performance.now()

    if (process.env.NODE_ENV === 'development' && endTime - startTime > 16) {
      console.warn(`Expensive computation took ${(endTime - startTime).toFixed(2)}ms`, {
        computation: computeFn.name || 'anonymous',
        duration: endTime - startTime
      })
    }
    
    return result
  }, [...deps, shouldCompute])
}

// FIXED: Virtual scrolling hook for large lists
export const useVirtualScrolling = <T,>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )

    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length])
  
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1).map(((item, index) => ({
      item,
      index: visibleRange.startIndex + index,
      top: (visibleRange.startIndex + index) * itemHeight
    }))
  }, [items, visibleRange, itemHeight])
  
  const totalHeight = items.length * itemHeight
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])
  
  return {
    visibleItems,
    totalHeight,
    handleScroll,
    visibleRange
  }
}

// FIXED: Intersection observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
): void => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const elementRef = useRef<HTMLElement>(null)
  useEffect(() => {
    const element = elementRef.current
    if (!element) return
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        setEntry(entry)
      },
      options
    )
    
    observer.observe(element)
    
    return () => {
      observer.unobserve(element)
    }
  }, [options])
  
  return { elementRef, isIntersecting, entry }
}

// FIXED: Optimized search hook with debouncing and caching
export const useOptimizedSearch = <T,>(
  items: T[],
  searchFn: (item: T, query: string) => boolean,
  debounceMs: number = 300
) => {
  const [query, setQuery] = useState('')
  const debouncedQuery = useDebounce(query, debounceMs)
  const cacheRef = useRef(new Map<string, T[]>())
  const filteredItems = useMemo(() => {
    if (!debouncedQuery.trim()) return items
    
    // Check cache first
    const cached = cacheRef.current.get(debouncedQuery)
    if (cached) return cached
    const startTime = performance.now()
    const filtered = items.filter(item => searchFn(item, debouncedQuery))
    const endTime = performance.now()
    
    // Cache result
    cacheRef.current.set(debouncedQuery, filtered)
    
    // Limit cache size
    if (cacheRef.current.size > 100) {
      const firstKey = cacheRef.current.keys().next().value
      cacheRef.current.delete(firstKey)
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`Search took ${(endTime - startTime).toFixed(2)}ms for "${debouncedQuery}"`)
    }
    
    return filtered
  }, [items, debouncedQuery, searchFn])
  
  // Clear cache when items change
  useEffect(() => {
    cacheRef.current.clear()
  }, [items])
  
  return {
    query,
    setQuery,
    filteredItems,
    isSearching: query !== debouncedQuery
  }
}

// FIXED: Batch state updates hook
export const useBatchedUpdates = <T,>() => {
  const [updates, setUpdates] = useState<T[]>([])
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const addUpdate = useCallback((update: T) => {
    setUpdates(prev => [...prev, update])
    
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    // Batch updates
    timeoutRef.current = setTimeout(() => {
      setUpdates([])
    }, 16) // Next frame
  }, [])
  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    const currentUpdates = updates
    setUpdates([])
    return currentUpdates
  }, [updates])
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])
  
  return { updates, addUpdate, flushUpdates }
}

// PERFORMANCE FIX: Optimized performance monitoring hook with throttling
export const usePerformanceMonitor = (componentName: string): void => {
  const renderCount = useRef(0)
  const lastRenderTime = useRef(Date.now())
  const lastWarningTime = useRef(0)
  useEffect(() => {
    renderCount.current++
    const now = Date.now()
    const timeSinceLastRender = now - lastRenderTime.current
    const timeSinceLastWarning = now - lastWarningTime.current
    lastRenderTime.current = now

    if (process.env.NODE_ENV === 'development') {
      // PERFORMANCE FIX: Throttle warnings to prevent console spam
      const shouldWarn = timeSinceLastRender < 16 &&
                        renderCount.current > 3 &&
                        timeSinceLastWarning > 5000 // Only warn every 5 seconds

      if (shouldWarn) {
        console.warn(`⚠️ ${componentName} re-rendered ${renderCount.current} times rapidly (${timeSinceLastRender}ms)`)
        lastWarningTime.current = now

        // Provide actionable advice
        console.info(`💡 Consider using React.memo, useMemo, or useCallback for ${componentName}`)
      }

      // PERFORMANCE FIX: Use requestIdleCallback for cleanup when available
      const scheduleReset = (): void => {
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => {
            renderCount.current = 0
          })
        } else {
          setTimeout(() => {
            renderCount.current = 0
          }, 1000)
        }
      }

      const timeout = setTimeout(scheduleReset, 2000) // Increased reset time
      return () => clearTimeout(timeout)
    }
  })

  return {
    renderCount: renderCount.current,
    lastRenderTime: lastRenderTime.current
  }
}

// FIXED: Optimized event handler hook
export const useOptimizedEventHandler = <T extends (...args: any[]) => any,>(handler: T,
  deps: React.DependencyList
): T => {
  const handlerRef = useRef(handler)
  useEffect(() => {
    handlerRef.current = handler
  })
  
  return useCallback(
    ((...args) => handlerRef.current(...args)) as T,
    deps
  )
}

// PERFORMANCE FIX: Optimized memory usage monitor with throttling
export const useMemoryMonitor = (componentName: string): void => {
  const lastMemoryCheck = useRef(0)
  const memoryBaseline = useRef<number | null>(null)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
      const now = Date.now()

      // PERFORMANCE FIX: Throttle memory checks to every 30 seconds
      if (now - lastMemoryCheck.current < 30000) return

      lastMemoryCheck.current = now

      const memory = performance.memory
      const currentUsed = Math.round(memory.usedJSHeapSize / 1048576) // MB

      // Set baseline on first check
      if (memoryBaseline.current === null) {
        memoryBaseline.current = currentUsed
      }

      const memoryInfo = {
        used: currentUsed,
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        baseline: memoryBaseline.current,
        growth: currentUsed - memoryBaseline.current
      }

      // PERFORMANCE FIX: Only log if significant change or high usage
      const significantChange = Math.abs(memoryInfo.growth) > 10 // 10MB change
      const highUsage = memoryInfo.used > 100 // 100MB threshold

      if (significantChange || highUsage) {
        console.log(`📊 ${componentName} memory:`, {
          current: `${memoryInfo.used}MB`,
          growth: `${memoryInfo.growth > 0 ? '+' : ''}${memoryInfo.growth}MB`,
          percentage: `${Math.round((memoryInfo.used / memoryInfo.limit) * 100)}%`
        })

        // PERFORMANCE FIX: Actionable warnings with memory leak detection
        if (memoryInfo.growth > 50) {
          console.warn(`🚨 ${componentName} potential memory leak: +${memoryInfo.growth}MB growth`)
          console.info(`💡 Check for: uncleaned event listeners, timers, or large object references`)
        } else if (highUsage) {
          console.warn(`⚠️ ${componentName} high memory usage: ${memoryInfo.used}MB`)
        }
      }
    }
  })
}

// FIXED: Optimized list rendering component
interface OptimizedListProps<T = any> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  keyExtractor: (item: T, index: number) => string | number
  itemHeight?: number
  containerHeight?: number
  className?: string
}

export const OptimizedList = <T = any,>({
  items,
  renderItem,
  keyExtractor,
  itemHeight = 50,
  containerHeight = 400,
  className = ''
}: OptimizedListProps<T>) => {
  const { visibleItems, totalHeight, handleScroll } = useVirtualScrolling(items,
    itemHeight,
    containerHeight)
  
  return (<div
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map((({ item, index, top }) => (<div
            key={keyExtractor(item, index)}
            style={{
              position: 'absolute',
              top,
              left: 0,
              right: 0,
              height: itemHeight
            }}
          >
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  )
}

export default {
  useDebounce,
  useThrottle,
  useExpensiveComputation,
  useVirtualScrolling,
  useIntersectionObserver,
  useOptimizedSearch,
  useBatchedUpdates,
  usePerformanceMonitor,
  useOptimizedEventHandler,
  useMemoryMonitor,
  OptimizedList
}
