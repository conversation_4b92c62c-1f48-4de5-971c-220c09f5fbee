import React from 'react';
/**
 * Advanced API Response Caching System
 * Intelligent caching with TTL, invalidation, compression, and performance optimization
 */

import { log } from './logger'

interface CacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  etag?: string
  lastModified?: string
  accessCount: number
  lastAccessed: number
  compressed?: boolean
  size?: number
}

interface CacheConfig {
  defaultTTL: number
  maxEntries: number
  enableCompression: boolean
  enableEncryption: boolean
  enablePersistence: boolean
  maxMemoryUsage: number // in MB
  compressionThreshold: number // in bytes
  enableAnalytics: boolean
}

class APICache {
  private cache = new Map<string, CacheEntry>()
  private config: CacheConfig
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      maxEntries: 100,
      enableCompression: true,
      enableEncryption: false,
      enablePersistence: true,
      maxMemoryUsage: 50, // 50MB
      compressionThreshold: 1024, // 1KB
      enableAnalytics: true,
      ...config
    }

    // Start cleanup interval
    this.startCleanup()

    // Load persisted cache
    if (this.config.enablePersistence) {
      this.loadPersistedCache()
    }
  }

  /**
   * Generate cache key from URL and parameters
   */
  private generateKey(url: string, params?: Record<string, any>): string {
    const baseKey: any = url.toLowerCase()
    if (!params) return baseKey

    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${JSON.stringify(params[key])}`)
      .join('&')

    return `${baseKey}?${sortedParams}`
  }

  /**
   * Check if cache entry is valid
   */
  private isValid(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp < entry.ttl
  }

  /**
   * Compress data if enabled
   */
  private async compressData(data: any): Promise<any> {
    if (!this.config.enableCompression) return data

    try {
      // Simple JSON compression (in real app, use proper compression library)
      return JSON.stringify(data)
    } catch {
      return data
    }
  }

  /**
   * Decompress data if enabled
   */
  private async decompressData(data: any): Promise<any> {
    if (!this.config.enableCompression) return data

    try {
      return typeof data === 'string' ? JSON.parse(data) : data
    } catch {
      return data
    }
  }

  /**
   * Set cache entry
   */
  async set<T>(
    url: string,
    data: T,
    options: {
      params?: Record<string, any>
      ttl?: number
      etag?: string
      lastModified?: string
    } = {}
  ): Promise<void> {
    const key = this.generateKey(url, options.params)
    const ttl = options.ttl || this.config.defaultTTL

    // Enforce max entries limit
    if (this.cache.size >= this.config.maxEntries) {
      this.evictOldest()
    }

    const compressedData = await this.compressData(data)

    const dataSize = new Blob([JSON.stringify(data)]).size

    const entry: CacheEntry<T> = {
      data: compressedData,
      timestamp: Date.now(),
      ttl,
      etag: options.etag,
      lastModified: options.lastModified,
      accessCount: 0,
      lastAccessed: Date.now(),
      compressed: this.config.enableCompression && dataSize > this.config.compressionThreshold,
      size: dataSize
    }

    this.cache.set(key, entry)
  }

  /**
   * Get cache entry
   */
  async get<T>(
    url: string,
    params?: Record<string, any>
  ): Promise<T | null> {
    const key = this.generateKey(url, params)
    const entry = this.cache.get(key)

    if (!entry || !this.isValid(entry)) {
      if (entry) {
        this.cache.delete(key)
      }
      return null
    }

    // PERFORMANCE IMPROVEMENT: Track access patterns
    entry.accessCount = (entry.accessCount || 0) + 1
    entry.lastAccessed = Date.now()

    const decompressedData = await this.decompressData(entry.data)
    return decompressedData as T
  }

  /**
   * Check if entry exists and is valid
   */
  has(url: string, params?: Record<string, any>): boolean {
    const key = this.generateKey(url, params)
    const entry = this.cache.get(key)
    return entry ? this.isValid(entry) : false
  }

  /**
   * Invalidate specific cache entry
   */
  invalidate(url: string, params?: Record<string, any>): void {
    const key = this.generateKey(url, params)
    this.cache.delete(key)
  }

  /**
   * Invalidate entries matching pattern
   */
  invalidatePattern(pattern: RegExp): void {
    const keysToDelete: string[] = []
    
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number
    maxEntries: number
    hitRate: number
    memoryUsage: number
    entries: Array<{ key: string; age: number; ttl: number; accessCount: number; size: number }>
  } {
    let totalSize: any = 0
    let totalAccess = 0

    const entries = Array.from(this.cache.entries()).map((([key, entry]) => {
      const size = entry.size || 0
      totalSize += size
      totalAccess += entry.accessCount || 0

      return {
        key,
        age: Date.now() - entry.timestamp,
        ttl: entry.ttl,
        accessCount: entry.accessCount || 0,
        size
      }
    })

    return {
      size: this.cache.size,
      maxEntries: this.config.maxEntries,
      hitRate: totalAccess > 0 ? (totalAccess / this.cache.size) : 0,
      memoryUsage: Math.round(totalSize / 1024 / 1024 * 100) / 100, // MB
      entries
    }
  }

  /**
   * PERFORMANCE IMPROVEMENT: Load persisted cache from localStorage
   */
  private loadPersistedCache(): void {
    try {
      const persistedData = localStorage.getItem('api_cache_data')
      if (persistedData) {
        const parsed = JSON.parse(persistedData)
        const now = Date.now()

        // Only load non-expired entries
        Object.entries(parsed).forEach((([key, entry]: [string, any]) => {
          if (entry.timestamp + entry.ttl > now) {
            this.cache.set(key, {
              ...entry,
              accessCount: entry.accessCount || 0,
              lastAccessed: entry.lastAccessed || entry.timestamp
            })
          }
        })

        log.info('cache', `Loaded ${this.cache.size} cached entries from persistence`)
      }
    } catch (error) {
      log.warn('cache', 'Failed to load persisted cache', error)
    }
  }

  /**
   * PERFORMANCE IMPROVEMENT: Persist cache to localStorage
   */
  private persistCache(): void {
    if (!this.config.enablePersistence) return

    try {
      const cacheData = Object.fromEntries(this.cache.entries())
      localStorage.setItem('api_cache_data', JSON.stringify(cacheData))
      log.debug('cache', 'Cache persisted to localStorage')
    } catch (error) {
      log.warn('cache', 'Failed to persist cache', error)
    }
  }

  /**
   * PERFORMANCE IMPROVEMENT: Smart eviction based on LRU and access patterns
   */
  private smartEviction(): void {
    const entries = Array.from(this.cache.entries())

    // Sort by access patterns (LRU + access frequency)
    entries.sort(([, a], [, b]) => {
      const aScore = (a.accessCount || 0) * 0.7 + (a.lastAccessed || a.timestamp) * 0.3
      const bScore = (b.accessCount || 0) * 0.7 + (b.lastAccessed || b.timestamp) * 0.3
      return aScore - bScore
    })

    // Remove least valuable entries
    const toRemove = Math.ceil(this.config.maxEntries * 0.1) // Remove 10%
    for (let i = 0; i < toRemove && entries.length > 0; i++) {
      const [key] = entries[i]
      this.cache.delete(key)
      log.debug('cache', `Evicted cache entry: ${key}`)
    }
  }

  /**
   * PERFORMANCE IMPROVEMENT: Evict entries using smart algorithm
   */
  private evictOldest(): void {
    // Use smart eviction if we have access pattern data
    const hasAccessData = Array.from(this.cache.values()).some(entry => entry.accessCount > 0)

    if (hasAccessData) {
      this.smartEviction()
    } else {
      // Fallback to simple LRU
      let oldestKey: string | null = null
      let oldestTimestamp = Date.now()

      for (const [key, entry] of this.cache.entries()) {
        if (entry.timestamp < oldestTimestamp) {
          oldestTimestamp = entry.timestamp
          oldestKey = key
        }
      }

      if (oldestKey) {
        this.cache.delete(oldestKey)
      }
    }
  }

  /**
   * Start cleanup interval
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const expiredKeys: string[] = []

      for (const [key, entry] of this.cache.entries()) {
        if (!this.isValid(entry)) {
          expiredKeys.push(key)
        }
      }

      expiredKeys.forEach(key => this.cache.delete(key))

      // PERFORMANCE IMPROVEMENT: Persist cache after cleanup
      if (expiredKeys.length > 0) {
        this.persistCache()
        log.debug('cache', `Cleaned up ${expiredKeys.length} expired entries`)
      }
    }, 60000) // Cleanup every minute

    // FIXED: Add cleanup on page unload to prevent memory leaks
    window.addEventListener('beforeunload', () => {
      this.persistCache()
      this.destroy()
    })
  }

  /**
   * FIXED: Destroy cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.cache.clear()
  }


}

// Cache configurations for different types of data
export const CACHE_CONFIGS = {
  // User data - cache for 10 minutes
  USER_DATA: {
    defaultTTL: 10 * 60 * 1000,
    maxEntries: 50
  },

  // Dashboard stats - cache for 2 minutes
  DASHBOARD: {
    defaultTTL: 2 * 60 * 1000,
    maxEntries: 20
  },

  // Employee data - cache for 5 minutes
  EMPLOYEES: {
    defaultTTL: 5 * 60 * 1000,
    maxEntries: 100
  },

  // Reports - cache for 15 minutes
  REPORTS: {
    defaultTTL: 15 * 60 * 1000,
    maxEntries: 30
  },

  // Static data - cache for 1 hour
  STATIC: {
    defaultTTL: 60 * 60 * 1000,
    maxEntries: 50
  }
}

// Create cache instances
export const userCache = new APICache(CACHE_CONFIGS.USER_DATA)
export const dashboardCache = new APICache(CACHE_CONFIGS.DASHBOARD)
export const employeeCache = new APICache(CACHE_CONFIGS.EMPLOYEES)
export const reportCache = new APICache(CACHE_CONFIGS.REPORTS)
export const staticCache = new APICache(CACHE_CONFIGS.STATIC)

// Cache-aware fetch wrapper
export const cachedFetch = async <T>(
  url: string,
  options: RequestInit & {
    cache?: APICache
    cacheKey?: string
    cacheTTL?: number
    params?: Record<string, any>
  } = {}
): Promise<T> => {
  const {
    cache = userCache,
    cacheKey = url,
    cacheTTL,
    params,
    ...fetchOptions
  } = options

  // Try to get from cache first
  const cachedData = await cache.get<T>(cacheKey, params)
  if (cachedData) {
    return cachedData
  }

  // Fetch from network
  const response = await fetch(url, fetchOptions)
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const data = await response.json()

  // Cache the response
  await cache.set(cacheKey, data, {
    params,
    ttl: cacheTTL,
    etag: response.headers.get('etag') || undefined,
    lastModified: response.headers.get('last-modified') || undefined
  })

  return data
}

// FIXED: Request deduplication to prevent duplicate API calls with better race condition handling
const pendingRequests = new Map<string, Promise<any>>()
const requestLocks = new Map<string, boolean>()
const lockWaitCounts = new Map<string, number>()

// FIXED: Improved deduplication to prevent race conditions with timeout and retry limits
export const deduplicateRequest = async <T>(
  key: string,
  requestFn: () => Promise<T>,
  ttl: number = 5000 // 5 seconds deduplication window
): Promise<T> => {
  // For search requests, use shorter TTL and be less aggressive
  const isSearchRequest = key.includes('"search"')
  const actualTtl = isSearchRequest ? Math.min(ttl, 200) : ttl

  // FIXED: Check for existing request with lock protection
  const existingRequest = pendingRequests.get(key)
  if (existingRequest && !requestLocks.get(key)) {
    console.log(`🔄 Deduplicating request: ${key}`)
    return existingRequest as Promise<T>
  }

  // CRITICAL FIX: Prevent infinite recursion with retry limits and timeout
  if (requestLocks.get(key)) {
    const waitCount = lockWaitCounts.get(key) || 0

    // Maximum 10 retries to prevent infinite recursion
    if (waitCount >= 10) {
      console.error(`🚨 Lock timeout for request: ${key}`)
      requestLocks.delete(key)
      lockWaitCounts.delete(key)
      throw new Error(`Request lock timeout: ${key}`)
    }

    lockWaitCounts.set(key, waitCount + 1)

    // Exponential backoff: 10ms, 20ms, 40ms, etc.
    const delay = Math.min(10 * Math.pow(2, waitCount), 1000)
    await new Promise(resolve => setTimeout(resolve, delay))

    return deduplicateRequest(key, requestFn, ttl)
  }

  requestLocks.set(key, true)
  lockWaitCounts.set(key, 0) // Reset wait count when acquiring lock

  try {
    // Double-check after acquiring lock
    const existingAfterLock = pendingRequests.get(key)
    if (existingAfterLock) {
      return existingAfterLock as Promise<T>
    }

    // FIXED: Create request with comprehensive cleanup
    const request = requestFn()
      .finally(() => {
        // Atomic cleanup - always remove from pending requests and release lock
        pendingRequests.delete(key)
        requestLocks.delete(key)
        lockWaitCounts.delete(key) // CRITICAL FIX: Clean up wait count
      })

    // Set the pending request
    pendingRequests.set(key, request)

    return request
  } catch (error) {
    // CRITICAL FIX: Ensure all locks and counters are released on error
    requestLocks.delete(key)
    lockWaitCounts.delete(key)
    throw error
  }
}

// CRITICAL FIX: Cleanup function to prevent memory leaks
export const cleanupDeduplicationCache = () => {
  pendingRequests.clear()
  requestLocks.clear()
  lockWaitCounts.clear()
  stopCleanupInterval()
  console.log('🧹 Deduplication cache cleaned up')
}

// Export cleanup interval management
export { startCleanupInterval, stopCleanupInterval }

// CRITICAL FIX: Auto-cleanup stale locks (run periodically) - MANAGED VERSION
let cleanupInterval: NodeJS.Timeout | null = null

const startCleanupInterval = () => {
  if (cleanupInterval) return // Prevent duplicate intervals

  cleanupInterval = setInterval(() => {
    const now = Date.now()
    const staleThreshold = 30000 // 30 seconds

    // Clean up any locks that might be stuck
    for (const [key, _] of requestLocks) {
      const waitCount = lockWaitCounts.get(key) || 0
      if (waitCount > 5) { // If waiting too long, force cleanup
        console.warn(`🧹 Cleaning up stale lock: ${key}`)
        requestLocks.delete(key)
        lockWaitCounts.delete(key)
        pendingRequests.delete(key)
      }
    }
  }, 30000) // Run every 30 seconds
}

const stopCleanupInterval = () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
    cleanupInterval = null
  }
}

// Start cleanup interval only when needed
if (typeof window !== 'undefined') {
  startCleanupInterval()

  // Clean up on page unload
  window.addEventListener('beforeunload', stopCleanupInterval)
}

// Cache invalidation helpers
export const invalidateUserCache = () => {
  userCache.invalidatePattern(/\/api\/auth\//)
  userCache.invalidatePattern(/\/api\/users\//)
}

export const invalidateEmployeeCache = (employeeId?: string) => {
  if (employeeId) {
    employeeCache.invalidatePattern(new RegExp(`/api/employees/${employeeId}`))
  } else {
    employeeCache.invalidatePattern(/\/api\/employees\//)
  }
}

export const invalidateDepartmentCache = (departmentId?: string) => {
  if (departmentId) {
    employeeCache.invalidatePattern(new RegExp(`/api/departments/${departmentId}`))
  } else {
    employeeCache.invalidatePattern(/\/api\/departments\//)
  }
  // Also invalidate employee cache since departments affect employee data
  employeeCache.invalidatePattern(/\/api\/employees\//)
}

export const invalidateDashboardCache = () => {
  dashboardCache.clear()
}

export const invalidateReportCache = () => {
  reportCache.clear()
}

// Browser storage caching for persistence
export const persistentCache = {
  set: (key: string, data: any, ttl: number = 24 * 60 * 60 * 1000): void => {
    try {
      const item = {
        data,
        timestamp: Date.now(),
        ttl
      }
      localStorage.setItem(`cache_${key}`, JSON.stringify(item))
    } catch {
      // Storage quota exceeded or disabled
    }
  },

  get: <T>(key: string): T | null => {
    try {
      const item = localStorage.getItem(`cache_${key}`)
      if (!item) return null

      const parsed = JSON.parse(item)
      if (Date.now() - parsed.timestamp > parsed.ttl) {
        localStorage.removeItem(`cache_${key}`)
        return null
      }

      return parsed.data as T
    } catch {
      return null
    }
  },

  remove: (key: string): void => {
    localStorage.removeItem(`cache_${key}`)
  },

  clear: (): void => {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'))
    keys.forEach(key => localStorage.removeItem(key))
  }
}

// Initialize cache system
export const initializeCacheSystem = (): void => {
  // Clear expired persistent cache entries on startup
  const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'))
  keys.forEach(key => {
    try {
      const item = JSON.parse(localStorage.getItem(key) || '{}')
      if (Date.now() - item.timestamp > item.ttl) {
        localStorage.removeItem(key)
      }
    } catch {
      localStorage.removeItem(key)
    }
  })

  // Set up cache invalidation on visibility change
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      // Invalidate short-lived caches when user returns
      dashboardCache.clear()
    }
  })
}

export default APICache
