import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  UserPlus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Phone,
  Mail,
  DollarSign,
  TrendingUp,
  Star,
  ShoppingCart,
  Target,
  Calendar
} from 'lucide-react'

interface SalesCustomersProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'عملاء المبيعات',
    subtitle: 'إدارة العملاء من منظور المبيعات والفرص التجارية',
    myCustomers: 'عملائي',
    potentialCustomers: 'عملاء محتملون',
    salesOpportunities: 'فرص المبيعات',
    monthlyTarget: 'الهدف الشهري',
    searchCustomers: 'البحث في العملاء...',
    addCustomer: 'إضافة عميل',
    exportData: 'تصدير البيانات',
    customerName: 'اسم العميل',
    company: 'الشركة',
    lastContact: 'آخر تواصل',
    nextFollowUp: 'المتابعة القادمة',
    salesValue: 'قيمة المبيعات',
    opportunities: 'الفرص',
    status: 'الحالة',
    actions: 'الإجراءات',
    viewProfile: 'عرض الملف الشخصي',
    contactCustomer: 'التواصل مع العميل',
    hot: 'ساخن',
    warm: 'دافئ',
    cold: 'بارد',
    qualified: 'مؤهل',
    contacted: 'تم التواصل',
    proposal: 'عرض',
    negotiation: 'تفاوض',
    closed: 'مغلق',
    refresh: 'تحديث',
    salesPipeline: 'خط أنابيب المبيعات',
    conversionRate: 'معدل التحويل',
    filterBy: 'تصفية حسب'
  },
  en: {
    title: 'Sales Customers',
    subtitle: 'Customer management from sales perspective and opportunities',
    myCustomers: 'My Customers',
    potentialCustomers: 'Potential Customers',
    salesOpportunities: 'Sales Opportunities',
    monthlyTarget: 'Monthly Target',
    searchCustomers: 'Search customers...',
    addCustomer: 'Add Customer',
    exportData: 'Export Data',
    customerName: 'Customer Name',
    company: 'Company',
    lastContact: 'Last Contact',
    nextFollowUp: 'Next Follow-up',
    salesValue: 'Sales Value',
    opportunities: 'Opportunities',
    status: 'Status',
    actions: 'Actions',
    viewProfile: 'View Profile',
    contactCustomer: 'Contact Customer',
    hot: 'Hot',
    warm: 'Warm',
    cold: 'Cold',
    qualified: 'Qualified',
    contacted: 'Contacted',
    proposal: 'Proposal',
    negotiation: 'Negotiation',
    closed: 'Closed',
    refresh: 'Refresh',
    salesPipeline: 'Sales Pipeline',
    conversionRate: 'Conversion Rate',
    filterBy: 'Filter By'
  }
}

export default function SalesCustomers({ language }: SalesCustomersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Sales-specific customer metrics
  const [salesMetrics, setSalesMetrics] = useState({
    myCustomers: 45,
    potentialCustomers: 23,
    salesOpportunities: 12,
    monthlyTarget: 150000,
    currentSales: 125000,
    conversionRate: 68.5
  })

  // Sample sales customer data
  const [customers] = useState([
    {
      id: 1,
      name: 'أحمد محمد الأحمد',
      nameEn: 'Ahmed Mohammed Al-Ahmed',
      company: 'شركة التقنية المتقدمة',
      companyEn: 'Advanced Technology Company',
      email: '<EMAIL>',
      phone: '+966501234567',
      lastContact: '2024-01-20',
      nextFollowUp: '2024-01-30',
      salesValue: 125000,
      opportunities: 3,
      status: 'hot',
      leadScore: 95,
      stage: 'negotiation'
    },
    {
      id: 2,
      name: 'فاطمة سالم العتيبي',
      nameEn: 'Fatima Salem Al-Otaibi',
      company: 'مؤسسة النور للتجارة',
      companyEn: 'Al-Noor Trading Est.',
      email: '<EMAIL>',
      phone: '+966507654321',
      lastContact: '2024-01-18',
      nextFollowUp: '2024-01-25',
      salesValue: 85000,
      opportunities: 2,
      status: 'warm',
      leadScore: 78,
      stage: 'proposal'
    },
    {
      id: 3,
      name: 'عمر خالد الشمري',
      nameEn: 'Omar Khalid Al-Shamri',
      company: 'شركة البناء الحديث',
      companyEn: 'Modern Construction Co.',
      email: '<EMAIL>',
      phone: '+966509876543',
      lastContact: '2024-01-15',
      nextFollowUp: '2024-01-28',
      salesValue: 195000,
      opportunities: 1,
      status: 'hot',
      leadScore: 88,
      stage: 'qualified'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'hot':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'warm':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'cold':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'qualified':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'qualified':
        return 'bg-green-500/20 text-green-400'
      case 'contacted':
        return 'bg-blue-500/20 text-blue-400'
      case 'proposal':
        return 'bg-purple-500/20 text-purple-400'
      case 'negotiation':
        return 'bg-orange-500/20 text-orange-400'
      case 'closed':
        return 'bg-green-500/20 text-green-400'
      default:
        return 'bg-gray-500/20 text-gray-400'
    }
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.company.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !selectedStatus || customer.status === selectedStatus

    return matchesSearch && matchesStatus
  })

  const salesMetricsCards = [
    {
      title: t.myCustomers,
      value: salesMetrics.myCustomers.toString(),
      change: '+5',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.potentialCustomers,
      value: salesMetrics.potentialCustomers.toString(),
      change: '+8',
      trend: 'up',
      icon: UserPlus,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.salesOpportunities,
      value: salesMetrics.salesOpportunities.toString(),
      change: '+3',
      trend: 'up',
      icon: Target,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.monthlyTarget,
      value: `${Math.round((salesMetrics.currentSales / salesMetrics.monthlyTarget) * 100)}%`,
      change: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(salesMetrics.currentSales),
      trend: 'up',
      icon: DollarSign,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calendar className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <UserPlus className="h-4 w-4 mr-2" />
            {t.addCustomer}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Sales Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {salesMetricsCards.map(((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' :
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">this month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchCustomers}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.status}</option>
              <option value="hot">{t.hot}</option>
              <option value="warm">{t.warm}</option>
              <option value="cold">{t.cold}</option>
              <option value="qualified">{t.qualified}</option>
            </select>

            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customer List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.salesPipeline}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredCustomers.map(((customer) => (
              <div key={customer.id} className="p-6 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-white font-semibold text-lg">
                        {language === 'ar' ? customer.name : customer.nameEn}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(customer.status)}`}>
                        {t[customer.status as keyof typeof t]}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStageColor(customer.stage)}`}>
                        {t[customer.stage as keyof typeof t]}
                      </span>
                    </div>
                    <p className="text-white/70 mb-3">
                      {language === 'ar' ? customer.company : customer.companyEn}
                    </p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-white/60">{t.lastContact}: </span>
                        <span className="text-white">{customer.lastContact}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.nextFollowUp}: </span>
                        <span className="text-white">{customer.nextFollowUp}</span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.salesValue}: </span>
                        <span className="text-white">
                          {new Intl.NumberFormat('ar-SA', {
                            style: 'currency',
                            currency: 'SAR'
                          }).format(customer.salesValue)}
                        </span>
                      </div>
                      <div>
                        <span className="text-white/60">{t.opportunities}: </span>
                        <span className="text-white">{customer.opportunities}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Mail className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <span className="text-white/60 text-sm">Lead Score:</span>
                      <div className="w-24 bg-white/20 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${customer.leadScore > 80 ? 'bg-green-500' : customer.leadScore > 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                          style={{ width: `${customer.leadScore}%` }}
                        ></div>
                      </div>
                      <span className="text-white text-sm font-medium">{customer.leadScore}%</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button size="sm" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                      <ShoppingCart className="h-4 w-4 mr-1" />
                      Create Opportunity
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
