import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Clock,
  Target,
  Users,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Filter,
  FileText
} from 'lucide-react'

interface ProjectReportsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    projectReports: 'تقارير المشاريع',
    generateReport: 'إنشاء تقرير',
    projectPerformance: 'أداء المشاريع',
    timeTracking: 'تتبع الوقت',
    budgetAnalysis: 'تحليل الميزانية',
    teamProductivity: 'إنتاجية الفريق',
    export: 'تصدير',
    filter: 'تصفية',
    projectName: 'اسم المشروع',
    progress: 'التقدم',
    budget: 'الميزانية',
    spent: 'المنفق',
    remaining: 'المتبقي',
    timeline: 'الجدول الزمني',
    status: 'الحالة',
    teamMembers: 'أعضاء الفريق',
    completedTasks: 'المهام المكتملة',
    totalTasks: 'إجمالي المهام',
    onTime: 'في الوقت المحدد',
    delayed: 'متأخر',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    planning: 'التخطيط',
    overallProgress: 'التقدم العام',
    budgetUtilization: 'استخدام الميزانية',
    taskCompletion: 'إكمال المهام',
    timeEfficiency: 'كفاءة الوقت',
    projectManager: 'مدير المشروع',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    actualEndDate: 'تاريخ الانتهاء الفعلي',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    efficiency: 'الكفاءة',
    riskLevel: 'مستوى المخاطر',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي'
  },
  en: {
    projectReports: 'Project Reports',
    generateReport: 'Generate Report',
    projectPerformance: 'Project Performance',
    timeTracking: 'Time Tracking',
    budgetAnalysis: 'Budget Analysis',
    teamProductivity: 'Team Productivity',
    export: 'Export',
    filter: 'Filter',
    projectName: 'Project Name',
    progress: 'Progress',
    budget: 'Budget',
    spent: 'Spent',
    remaining: 'Remaining',
    timeline: 'Timeline',
    status: 'Status',
    teamMembers: 'Team Members',
    completedTasks: 'Completed Tasks',
    totalTasks: 'Total Tasks',
    onTime: 'On Time',
    delayed: 'Delayed',
    completed: 'Completed',
    inProgress: 'In Progress',
    planning: 'Planning',
    overallProgress: 'Overall Progress',
    budgetUtilization: 'Budget Utilization',
    taskCompletion: 'Task Completion',
    timeEfficiency: 'Time Efficiency',
    projectManager: 'Project Manager',
    startDate: 'Start Date',
    endDate: 'End Date',
    actualEndDate: 'Actual End Date',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    efficiency: 'Efficiency',
    riskLevel: 'Risk Level',
    low: 'Low',
    medium: 'Medium',
    high: 'High'
  }
}

export default function ProjectReports({ language }: ProjectReportsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState('2024-01')
  const [activeTab, setActiveTab] = useState('performance')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Mock data
  const overallStats = {
    overallProgress: 68,
    budgetUtilization: 72,
    taskCompletion: 75,
    timeEfficiency: 85
  }

  const projectPerformance = [
    {
      id: 1,
      name: 'نظام إدارة المخزون',
      manager: 'أحمد محمد',
      progress: 75,
      budget: 150000,
      spent: 108000,
      remaining: 42000,
      status: 'inProgress',
      teamMembers: 8,
      completedTasks: 34,
      totalTasks: 45,
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      estimatedHours: 1200,
      actualHours: 890,
      riskLevel: 'low'
    },
    {
      id: 2,
      name: 'تطوير تطبيق الموبايل',
      manager: 'فاطمة علي',
      progress: 45,
      budget: 200000,
      spent: 95000,
      remaining: 105000,
      status: 'inProgress',
      teamMembers: 6,
      completedTasks: 14,
      totalTasks: 32,
      startDate: '2024-02-15',
      endDate: '2024-08-15',
      estimatedHours: 1600,
      actualHours: 720,
      riskLevel: 'medium'
    },
    {
      id: 3,
      name: 'تحديث نظام المحاسبة',
      manager: 'محمد حسن',
      progress: 100,
      budget: 80000,
      spent: 78000,
      remaining: 2000,
      status: 'completed',
      teamMembers: 4,
      completedTasks: 28,
      totalTasks: 28,
      startDate: '2023-10-01',
      endDate: '2024-01-31',
      estimatedHours: 800,
      actualHours: 820,
      riskLevel: 'low'
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'planning':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const calculateEfficiency = (estimated: number, actual: number) => {
    if (actual === 0) return 100
    return Math.round((estimated / actual) * 100)
  }

  const isDelayed = (endDate: string, progress: number) => {
    const end = new Date(endDate)
    const today = new Date()
    const totalDuration = end.getTime() - new Date('2024-01-01').getTime()
    const elapsed = today.getTime() - new Date('2024-01-01').getTime()
    const expectedProgress = (elapsed / totalDuration) * 100
    return progress < expectedProgress && progress < 100
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.projectReports}</h1>
          <p className="text-white/70">تحليل أداء المشاريع والتقارير التفصيلية</p>
        </div>
        <div className="flex gap-2">
          <Button className="glass-button">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
        </div>
      </div>

      {/* Overall Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.overallProgress}</p>
                <p className="text-2xl font-bold text-blue-400">{overallStats.overallProgress}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
            <div className="mt-3 w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full"
                style={{ width: `${overallStats.overallProgress}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.budgetUtilization}</p>
                <p className="text-2xl font-bold text-green-400">{overallStats.budgetUtilization}%</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-400" />
            </div>
            <div className="mt-3 w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full"
                style={{ width: `${overallStats.budgetUtilization}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.taskCompletion}</p>
                <p className="text-2xl font-bold text-purple-400">{overallStats.taskCompletion}%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-purple-400" />
            </div>
            <div className="mt-3 w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full"
                style={{ width: `${overallStats.taskCompletion}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.timeEfficiency}</p>
                <p className="text-2xl font-bold text-orange-400">{overallStats.timeEfficiency}%</p>
              </div>
              <Clock className="h-8 w-8 text-orange-400" />
            </div>
            <div className="mt-3 w-full bg-white/20 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-orange-500 to-orange-600 h-2 rounded-full"
                style={{ width: `${overallStats.timeEfficiency}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/10 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('performance')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
            activeTab === 'performance'
              ? 'bg-white/20 text-white shadow-lg'
              : 'text-white/70 hover:text-white hover:bg-white/10'
          }`}
        >
          {t.projectPerformance}
        </button>
        <button
          onClick={() => setActiveTab('budget')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
            activeTab === 'budget'
              ? 'bg-white/20 text-white shadow-lg'
              : 'text-white/70 hover:text-white hover:bg-white/10'
          }`}
        >
          {t.budgetAnalysis}
        </button>
        <button
          onClick={() => setActiveTab('time')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
            activeTab === 'time'
              ? 'bg-white/20 text-white shadow-lg'
              : 'text-white/70 hover:text-white hover:bg-white/10'
          }`}
        >
          {t.timeTracking}
        </button>
      </div>

      {/* Project Performance Table */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.projectPerformance}</CardTitle>
              <CardDescription className="text-white/70">
                تحليل مفصل لأداء جميع المشاريع
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Input
                type="month"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="glass-input"
              />
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.projectName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.projectManager}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.progress}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.budget}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.timeline}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.efficiency}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.riskLevel}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                </tr>
              </thead>
              <tbody>
                {projectPerformance.map(((project) => (
                  <tr key={project.id} className="border-b border-white/10 hover:bg-white/5">
                    <td className="py-3 px-4">
                      <div>
                        <p className="text-white font-medium">{project.name}</p>
                        <p className="text-white/60 text-sm">{project.teamMembers} أعضاء • {project.completedTasks}/{project.totalTasks} مهام</p>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-white/80">{project.manager}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-white/20 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                            style={{ width: `${project.progress}%` }}
                          ></div>
                        </div>
                        <span className="text-white text-sm">{project.progress}%</span>
                        {isDelayed(project.endDate, project.progress) && (
                          <AlertTriangle className="h-4 w-4 text-red-400" />
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <p className="text-white text-sm">{formatCurrency(project.spent)} / {formatCurrency(project.budget)}</p>
                        <p className="text-white/60 text-xs">{Math.round((project.spent / project.budget) * 100)}% مستخدم</p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <p className="text-white/80">{project.startDate}</p>
                        <p className="text-white/80">{project.endDate}</p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <p className="text-white text-sm">{calculateEfficiency(project.estimatedHours, project.actualHours)}%</p>
                        <p className="text-white/60 text-xs">{project.actualHours}h / {project.estimatedHours}h</p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`font-medium ${getRiskColor(project.riskLevel)}`}>
                        {t[project.riskLevel as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                        {t[project.status as keyof typeof t]}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
