import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  MessageSquare, 
  Send, 
  Search, 
  Plus,
  User,
  Clock,
  Check,
  CheckCheck,
  Paperclip,
  Smile,
  MoreVertical
} from 'lucide-react'

interface PersonalMessagesProps {
  language: 'ar' | 'en'
}

interface Message {
  id: string
  senderId: string
  senderName: string
  senderNameAr: string
  content: string
  timestamp: string
  read: boolean
  type: 'sent' | 'received'
}

interface Conversation {
  id: string
  participantId: string
  participantName: string
  participantNameAr: string
  participantRole: string
  participantRoleAr: string
  lastMessage: string
  lastMessageTime: string
  unreadCount: number
  online: boolean
  avatar?: string
}

const mockConversations: Conversation[] = [
  {
    id: '1',
    participantId: 'user1',
    participantName: '<PERSON>',
    participantNameAr: 'أحمد حسن',
    participantRole: 'HR Manager',
    participantRoleAr: 'مدير الموارد البشرية',
    lastMessage: 'مرحباً، كيف يمكنني مساعدتك؟',
    lastMessageTime: '10:30 AM',
    unreadCount: 2,
    online: true
  },
  {
    id: '2',
    participantId: 'user2',
    participantName: 'Sara Ali',
    participantNameAr: 'سارة علي',
    participantRole: 'Finance Manager',
    participantRoleAr: 'مدير مالي',
    lastMessage: 'تم إرسال التقرير المالي',
    lastMessageTime: '9:15 AM',
    unreadCount: 0,
    online: false
  },
  {
    id: '3',
    participantId: 'user3',
    participantName: 'Omar Khalil',
    participantNameAr: 'عمر خليل',
    participantRole: 'Sales Manager',
    participantRoleAr: 'مدير مبيعات',
    lastMessage: 'هل يمكننا مناقشة العرض الجديد؟',
    lastMessageTime: 'Yesterday',
    unreadCount: 1,
    online: true
  }
]

const mockMessages: Message[] = [
  {
    id: '1',
    senderId: 'user1',
    senderName: 'Ahmed Hassan',
    senderNameAr: 'أحمد حسن',
    content: 'مرحباً، كيف يمكنني مساعدتك؟',
    timestamp: '10:30 AM',
    read: true,
    type: 'received'
  },
  {
    id: '2',
    senderId: 'me',
    senderName: 'Me',
    senderNameAr: 'أنا',
    content: 'أحتاج إلى مساعدة في طلب الإجازة',
    timestamp: '10:32 AM',
    read: true,
    type: 'sent'
  },
  {
    id: '3',
    senderId: 'user1',
    senderName: 'Ahmed Hassan',
    senderNameAr: 'أحمد حسن',
    content: 'بالطبع، يمكنك تقديم طلب الإجازة من خلال النظام',
    timestamp: '10:35 AM',
    read: false,
    type: 'received'
  }
]

export default function PersonalMessages({ language }: PersonalMessagesProps) {
  const [conversations, setConversations] = useState<Conversation[]>(mockConversations)
  const [selectedConversation, setSelectedConversation] = useState<string | null>(conversations[0]?.id || null)
  const [messages, setMessages] = useState<Message[]>(mockMessages)
  const [newMessage, setNewMessage] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  const text = {
    ar: {
      title: 'رسائلي الشخصية',
      description: 'تواصل مع زملائك في العمل',
      search: 'البحث في المحادثات...',
      newChat: 'محادثة جديدة',
      online: 'متصل',
      offline: 'غير متصل',
      typeMessage: 'اكتب رسالة...',
      send: 'إرسال',
      today: 'اليوم',
      yesterday: 'أمس',
      noConversations: 'لا توجد محادثات',
      noMessages: 'لا توجد رسائل',
      selectConversation: 'اختر محادثة لبدء المراسلة'
    },
    en: {
      title: 'My Personal Messages',
      description: 'Communicate with your colleagues',
      search: 'Search conversations...',
      newChat: 'New Chat',
      online: 'Online',
      offline: 'Offline',
      typeMessage: 'Type a message...',
      send: 'Send',
      today: 'Today',
      yesterday: 'Yesterday',
      noConversations: 'No conversations',
      noMessages: 'No messages',
      selectConversation: 'Select a conversation to start messaging'
    }
  }

  const t = text[language]

  const filteredConversations = conversations.filter(conv =>
    conv.participantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.participantNameAr.includes(searchTerm)
  )

  const selectedConv = conversations.find(conv => conv.id === selectedConversation)

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return

    const message: Message = {
      id: Date.now().toString(),
      senderId: 'me',
      senderName: 'Me',
      senderNameAr: 'أنا',
      content: newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      read: true,
      type: 'sent'
    }

    setMessages(prev => [...prev, message])
    setNewMessage('')

    // Update last message in conversation
    setConversations(prev =>
      prev.map(conv =>
        conv.id === selectedConversation
          ? { ...conv, lastMessage: newMessage, lastMessageTime: message.timestamp }
          : conv
      )
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
        <div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
            {t.title}
          </h1>
          <p className="text-white/80 mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
            {t.description}
          </p>
        </div>
        <Button className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base">
          <Plus className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
          {t.newChat}
        </Button>
      </div>

      {/* Messages Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
        {/* Conversations List */}
        <Card className="modern-card border-0 lg:col-span-1">
          <CardHeader className="p-4 sm:p-6 border-b border-white/10">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-4 w-4" />
              <Input
                placeholder={t.search}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pl-10"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="max-h-[500px] overflow-y-auto">
              {filteredConversations.map(((conv) => (
                <div
                  key={conv.id}
                  onClick={() => setSelectedConversation(conv.id)}
                  className={`p-4 border-b border-white/5 cursor-pointer transition-all duration-300 hover:bg-white/5 ${
                    selectedConversation === conv.id ? 'bg-white/10 border-r-2 border-r-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className="relative">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                        {conv.avatar || conv.participantName.charAt(0)}
                      </div>
                      {conv.online && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-white font-medium text-sm truncate">
                          {language === 'ar' ? conv.participantNameAr : conv.participantName}
                        </h3>
                        <span className="text-white/60 text-xs">{conv.lastMessageTime}</span>
                      </div>
                      <p className="text-white/60 text-xs mb-1">
                        {language === 'ar' ? conv.participantRoleAr : conv.participantRole}
                      </p>
                      <div className="flex items-center justify-between">
                        <p className="text-white/70 text-xs truncate flex-1">
                          {conv.lastMessage}
                        </p>
                        {conv.unreadCount > 0 && (
                          <Badge className="bg-blue-500 text-white text-xs ml-2">
                            {conv.unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Chat Area */}
        <Card className="modern-card border-0 lg:col-span-2 flex flex-col">
          {selectedConv ? (
            <>
              {/* Chat Header */}
              <CardHeader className="p-4 sm:p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                        {selectedConv.avatar || selectedConv.participantName.charAt(0)}
                      </div>
                      {selectedConv.online && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-white font-medium">
                        {language === 'ar' ? selectedConv.participantNameAr : selectedConv.participantName}
                      </h3>
                      <p className="text-white/60 text-sm">
                        {selectedConv.online ? t.online : t.offline}
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 p-4 sm:p-6 overflow-y-auto">
                <div className="space-y-4">
                  {messages.map(((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === 'sent' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[70%] p-3 rounded-lg ${
                          message.type === 'sent'
                            ? 'bg-blue-500 text-white'
                            : 'glass border border-white/20 text-white'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs opacity-70">{message.timestamp}</span>
                          {message.type === 'sent' && (
                            <div className="ml-2">
                              {message.read ? (
                                <CheckCheck className="h-3 w-3 text-blue-200" />
                              ) : (
                                <Check className="h-3 w-3 text-blue-200" />
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>

              {/* Message Input */}
              <div className="p-4 sm:p-6 border-t border-white/10">
                <div className="flex items-center gap-3">
                  <Button variant="ghost" size="sm" className="text-white/70 hover:text-white p-2">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  <div className="flex-1 relative">
                    <Input
                      placeholder={t.typeMessage}
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      className="glass text-white placeholder-white/60 border-white/30 focus:border-white/50 pr-12"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white p-1"
                    >
                      <Smile className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 text-white p-2"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="h-16 w-16 text-white/40 mx-auto mb-4" />
                <p className="text-white/60">{t.selectConversation}</p>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
