import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Briefcase,
  Edit,
  Save,
  X,
  Camera,
  Shield,
  Award,
  Clock,
  Building,
  Users,
  Star
} from 'lucide-react'

interface PersonalProfileProps {
  language: 'ar' | 'en'
}

interface UserProfile {
  id: string
  firstName: string
  firstNameAr: string
  lastName: string
  lastNameAr: string
  email: string
  phone: string
  position: string
  positionAr: string
  department: string
  departmentAr: string
  manager: string
  managerAr: string
  joinDate: string
  location: string
  locationAr: string
  avatar?: string
  bio: string
  bioAr: string
  skills: string[]
  skillsAr: string[]
  achievements: Array<{
    title: string
    titleAr: string
    date: string
    description: string
    descriptionAr: string
  }>
  workHours: string
  employeeId: string
  status: 'active' | 'inactive' | 'on_leave'
}

const mockProfile: UserProfile = {
  id: '1',
  firstName: 'Ahmed',
  firstNameAr: 'أحمد',
  lastName: '<PERSON>',
  lastNameAr: 'حسن',
  email: '<EMAIL>',
  phone: '+966 50 123 4567',
  position: 'Senior Developer',
  positionAr: 'مطور أول',
  department: 'IT Department',
  departmentAr: 'قسم تقنية المعلومات',
  manager: 'Sara Ali',
  managerAr: 'سارة علي',
  joinDate: '2022-03-15',
  location: 'Riyadh, Saudi Arabia',
  locationAr: 'الرياض، المملكة العربية السعودية',
  bio: 'Experienced software developer with expertise in React and Node.js',
  bioAr: 'مطور برمجيات ذو خبرة في React و Node.js',
  skills: ['React', 'TypeScript', 'Node.js', 'Python', 'AWS'],
  skillsAr: ['React', 'TypeScript', 'Node.js', 'Python', 'AWS'],
  achievements: [
    {
      title: 'Employee of the Month',
      titleAr: 'موظف الشهر',
      date: '2023-12-01',
      description: 'Outstanding performance in Q4 2023',
      descriptionAr: 'أداء متميز في الربع الرابع 2023'
    },
    {
      title: 'Project Excellence Award',
      titleAr: 'جائزة التميز في المشروع',
      date: '2023-09-15',
      description: 'Led successful implementation of new system',
      descriptionAr: 'قاد التنفيذ الناجح للنظام الجديد'
    }
  ],
  workHours: '9:00 AM - 6:00 PM',
  employeeId: 'EMP001',
  status: 'active'
}

export default function PersonalProfile({ language }: PersonalProfileProps) {
  const [profile, setProfile] = useState<UserProfile>(mockProfile)
  const [isEditing, setIsEditing] = useState(false)
  const [editedProfile, setEditedProfile] = useState<UserProfile>(mockProfile)

  const text = {
    ar: {
      title: 'ملفي الشخصي',
      description: 'إدارة معلوماتي الشخصية والمهنية',
      edit: 'تعديل',
      save: 'حفظ',
      cancel: 'إلغاء',
      personalInfo: 'المعلومات الشخصية',
      workInfo: 'معلومات العمل',
      skills: 'المهارات',
      achievements: 'الإنجازات',
      firstName: 'الاسم الأول',
      lastName: 'اسم العائلة',
      email: 'البريد الإلكتروني',
      phone: 'رقم الهاتف',
      position: 'المنصب',
      department: 'القسم',
      manager: 'المدير المباشر',
      joinDate: 'تاريخ الانضمام',
      location: 'الموقع',
      bio: 'نبذة شخصية',
      workHours: 'ساعات العمل',
      employeeId: 'رقم الموظف',
      status: 'الحالة',
      active: 'نشط',
      inactive: 'غير نشط',
      on_leave: 'في إجازة',
      changePhoto: 'تغيير الصورة',
      addSkill: 'إضافة مهارة',
      noAchievements: 'لا توجد إنجازات'
    },
    en: {
      title: 'My Personal Profile',
      description: 'Manage my personal and professional information',
      edit: 'Edit',
      save: 'Save',
      cancel: 'Cancel',
      personalInfo: 'Personal Information',
      workInfo: 'Work Information',
      skills: 'Skills',
      achievements: 'Achievements',
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      position: 'Position',
      department: 'Department',
      manager: 'Manager',
      joinDate: 'Join Date',
      location: 'Location',
      bio: 'Bio',
      workHours: 'Work Hours',
      employeeId: 'Employee ID',
      status: 'Status',
      active: 'Active',
      inactive: 'Inactive',
      on_leave: 'On Leave',
      changePhoto: 'Change Photo',
      addSkill: 'Add Skill',
      noAchievements: 'No achievements'
    }
  }

  const t = text[language]

  const handleSave = () => {
    setProfile(editedProfile)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditedProfile(profile)
    setIsEditing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'inactive': return 'bg-red-500'
      case 'on_leave': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  const calculateYearsOfService = (joinDate: string) => {
    const join = new Date(joinDate)
    const now = new Date()
    const years = now.getFullYear() - join.getFullYear()
    return years
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
        <div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
            {t.title}
          </h1>
          <p className="text-white/80 mt-1 sm:mt-2 text-sm sm:text-base lg:text-lg">
            {t.description}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {isEditing ? (
            <>
              <Button
                onClick={handleSave}
                className="gradient-bg-green hover:scale-105 transform transition-all duration-300 text-white font-semibold px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base"
              >
                <Save className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                {t.save}
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                className="glass-button text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3"
              >
                <X className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                {t.cancel}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 text-white font-semibold px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base"
            >
              <Edit className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
              {t.edit}
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Overview */}
        <Card className="modern-card border-0">
          <CardContent className="p-6 text-center">
            <div className="relative inline-block mb-6">
              <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-2xl sm:text-4xl font-bold mx-auto">
                {profile.avatar || `${profile.firstName.charAt(0)}${profile.lastName.charAt(0)}`}
              </div>
              {isEditing && (
                <Button
                  size="sm"
                  className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0 bg-blue-500 hover:bg-blue-600"
                >
                  <Camera className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">
              {language === 'ar' 
                ? `${profile.firstNameAr} ${profile.lastNameAr}`
                : `${profile.firstName} ${profile.lastName}`
              }
            </h2>
            
            <p className="text-white/70 mb-4">
              {language === 'ar' ? profile.positionAr : profile.position}
            </p>
            
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(profile.status)}`}></div>
              <span className="text-white/70 text-sm">
                {t[profile.status as keyof typeof t] || profile.status}
              </span>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-center gap-2 text-white/70">
                <Building className="h-4 w-4" />
                <span>{language === 'ar' ? profile.departmentAr : profile.department}</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-white/70">
                <MapPin className="h-4 w-4" />
                <span>{language === 'ar' ? profile.locationAr : profile.location}</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-white/70">
                <Calendar className="h-4 w-4" />
                <span>{calculateYearsOfService(profile.joinDate)} years of service</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Card className="modern-card border-0">
            <CardHeader className="p-6 border-b border-white/10">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <User className="h-5 w-5" />
                {t.personalInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <Label className="text-white/70 text-sm">{t.firstName}</Label>
                  {isEditing ? (
                    <Input
                      value={language === 'ar' ? editedProfile.firstNameAr : editedProfile.firstName}
                      onChange={(e) => setEditedProfile(prev => ({
                        ...prev,
                        [language === 'ar' ? 'firstNameAr' : 'firstName']: e.target.value
                      }))}
                      className="glass text-white mt-1"
                    />
                  ) : (
                    <p className="text-white mt-1">
                      {language === 'ar' ? profile.firstNameAr : profile.firstName}
                    </p>
                  )}
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.lastName}</Label>
                  {isEditing ? (
                    <Input
                      value={language === 'ar' ? editedProfile.lastNameAr : editedProfile.lastName}
                      onChange={(e) => setEditedProfile(prev => ({
                        ...prev,
                        [language === 'ar' ? 'lastNameAr' : 'lastName']: e.target.value
                      }))}
                      className="glass text-white mt-1"
                    />
                  ) : (
                    <p className="text-white mt-1">
                      {language === 'ar' ? profile.lastNameAr : profile.lastName}
                    </p>
                  )}
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.email}</Label>
                  {isEditing ? (
                    <Input
                      value={editedProfile.email}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, email: e.target.value }))}
                      className="glass text-white mt-1"
                    />
                  ) : (
                    <p className="text-white mt-1 flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      {profile.email}
                    </p>
                  )}
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.phone}</Label>
                  {isEditing ? (
                    <Input
                      value={editedProfile.phone}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, phone: e.target.value }))}
                      className="glass text-white mt-1"
                    />
                  ) : (
                    <p className="text-white mt-1 flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {profile.phone}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="mt-6">
                <Label className="text-white/70 text-sm">{t.bio}</Label>
                {isEditing ? (
                  <textarea
                    value={language === 'ar' ? editedProfile.bioAr : editedProfile.bio}
                    onChange={(e) => setEditedProfile(prev => ({
                      ...prev,
                      [language === 'ar' ? 'bioAr' : 'bio']: e.target.value
                    }))}
                    className="w-full mt-1 p-3 glass text-white rounded-lg border border-white/30 focus:border-white/50 resize-none"
                    rows={3}
                  />
                ) : (
                  <p className="text-white mt-1">
                    {language === 'ar' ? profile.bioAr : profile.bio}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Work Information */}
          <Card className="modern-card border-0">
            <CardHeader className="p-6 border-b border-white/10">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                {t.workInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <Label className="text-white/70 text-sm">{t.employeeId}</Label>
                  <p className="text-white mt-1">{profile.employeeId}</p>
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.position}</Label>
                  <p className="text-white mt-1">
                    {language === 'ar' ? profile.positionAr : profile.position}
                  </p>
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.department}</Label>
                  <p className="text-white mt-1">
                    {language === 'ar' ? profile.departmentAr : profile.department}
                  </p>
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.manager}</Label>
                  <p className="text-white mt-1 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    {language === 'ar' ? profile.managerAr : profile.manager}
                  </p>
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.joinDate}</Label>
                  <p className="text-white mt-1 flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {new Date(profile.joinDate).toLocaleDateString()}
                  </p>
                </div>
                
                <div>
                  <Label className="text-white/70 text-sm">{t.workHours}</Label>
                  <p className="text-white mt-1 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    {profile.workHours}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skills */}
          <Card className="modern-card border-0">
            <CardHeader className="p-6 border-b border-white/10">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Star className="h-5 w-5" />
                {t.skills}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex flex-wrap gap-2">
                {(language === 'ar' ? profile.skillsAr : profile.skills).map(((skill, index) => (
                  <Badge key={index} className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    {skill}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card className="modern-card border-0">
            <CardHeader className="p-6 border-b border-white/10">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Award className="h-5 w-5" />
                {t.achievements}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {profile.achievements.length > 0 ? (
                <div className="space-y-4">
                  {profile.achievements.map(((achievement, index) => (
                    <div key={index} className="glass-card border-white/10 p-4 rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-white font-medium">
                          {language === 'ar' ? achievement.titleAr : achievement.title}
                        </h3>
                        <span className="text-white/60 text-sm">
                          {new Date(achievement.date).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-white/70 text-sm">
                        {language === 'ar' ? achievement.descriptionAr : achievement.description}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Award className="h-12 w-12 text-white/40 mx-auto mb-3" />
                  <p className="text-white/60">{t.noAchievements}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
