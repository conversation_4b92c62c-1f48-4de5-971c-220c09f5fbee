import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  Award,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Download,
  Calendar,
  User,
  CheckCircle,
  Clock
} from 'lucide-react'

interface CertificationsProps {
  language: 'ar' | 'en'
}

interface Certification {
  id: string
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  issuingOrganization: string
  issuingOrganizationAr: string
  employee: string
  employeeAr: string
  employeeId: string
  issueDate: string
  expiryDate: string
  certificateNumber: string
  status: 'active' | 'expired' | 'pending' | 'revoked'
  category: string
  categoryAr: string
  level: 'basic' | 'intermediate' | 'advanced' | 'expert'
  verificationUrl?: string
  attachmentUrl?: string
}

const mockCertifications: Certification[] = [
  {
    id: '1',
    name: 'Project Management Professional (PMP)',
    nameAr: 'محترف إدارة المشاريع',
    description: 'Professional certification in project management',
    descriptionAr: 'شهادة مهنية في إدارة المشاريع',
    issuingOrganization: 'Project Management Institute',
    issuingOrganizationAr: 'معهد إدارة المشاريع',
    employee: 'Ahmed Al-Rashid',
    employeeAr: 'أحمد الراشد',
    employeeId: 'EMP001',
    issueDate: '2023-06-15',
    expiryDate: '2026-06-15',
    certificateNumber: 'PMP-2023-001',
    status: 'active',
    category: 'Management',
    categoryAr: 'الإدارة',
    level: 'advanced',
    verificationUrl: 'https://pmi.org/verify/PMP-2023-001'
  },
  {
    id: '2',
    name: 'Certified Public Accountant (CPA)',
    nameAr: 'محاسب قانوني معتمد',
    description: 'Professional accounting certification',
    descriptionAr: 'شهادة مهنية في المحاسبة',
    issuingOrganization: 'AICPA',
    issuingOrganizationAr: 'المعهد الأمريكي للمحاسبين القانونيين',
    employee: 'Fatima Hassan',
    employeeAr: 'فاطمة حسن',
    employeeId: 'EMP002',
    issueDate: '2022-12-10',
    expiryDate: '2025-12-10',
    certificateNumber: 'CPA-2022-045',
    status: 'active',
    category: 'Finance',
    categoryAr: 'المالية',
    level: 'expert',
    verificationUrl: 'https://aicpa.org/verify/CPA-2022-045'
  },
  {
    id: '3',
    name: 'Digital Marketing Certificate',
    nameAr: 'شهادة التسويق الرقمي',
    description: 'Certificate in digital marketing strategies',
    descriptionAr: 'شهادة في استراتيجيات التسويق الرقمي',
    issuingOrganization: 'Google',
    issuingOrganizationAr: 'جوجل',
    employee: 'Sarah Mohammed',
    employeeAr: 'سارة محمد',
    employeeId: 'EMP003',
    issueDate: '2024-01-20',
    expiryDate: '2025-01-20',
    certificateNumber: 'GDMC-2024-078',
    status: 'active',
    category: 'Marketing',
    categoryAr: 'التسويق',
    level: 'intermediate',
    verificationUrl: 'https://skillshop.exceedlms.com/verify/GDMC-2024-078'
  },
  {
    id: '4',
    name: 'ISO 9001 Lead Auditor',
    nameAr: 'مدقق رئيسي آيزو 9001',
    description: 'Lead auditor certification for ISO 9001',
    descriptionAr: 'شهادة مدقق رئيسي لآيزو 9001',
    issuingOrganization: 'BSI Group',
    issuingOrganizationAr: 'مجموعة بي إس آي',
    employee: 'Omar Abdullah',
    employeeAr: 'عمر عبدالله',
    employeeId: 'EMP004',
    issueDate: '2021-08-15',
    expiryDate: '2024-08-15',
    certificateNumber: 'ISO-LA-2021-032',
    status: 'expired',
    category: 'Quality',
    categoryAr: 'الجودة',
    level: 'expert'
  }
]

export default function Certifications({ language }: CertificationsProps) {
  const [certifications, setCertifications] = useState<Certification[]>(mockCertifications)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'الشهادات المهنية',
      description: 'إدارة ومتابعة الشهادات المهنية للموظفين',
      newCertification: 'شهادة جديدة',
      search: 'البحث في الشهادات...',
      certificationName: 'اسم الشهادة',
      employee: 'الموظف',
      issuingOrg: 'الجهة المصدرة',
      issueDate: 'تاريخ الإصدار',
      expiryDate: 'تاريخ الانتهاء',
      status: 'الحالة',
      level: 'المستوى',
      category: 'الفئة',
      certificateNumber: 'رقم الشهادة',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      download: 'تحميل',
      verify: 'التحقق',
      totalCertifications: 'إجمالي الشهادات',
      activeCertifications: 'الشهادات النشطة',
      expiringCertifications: 'الشهادات المنتهية قريباً',
      active: 'نشط',
      expired: 'منتهي',
      pending: 'معلق',
      revoked: 'ملغي',
      basic: 'أساسي',
      intermediate: 'متوسط',
      advanced: 'متقدم',
      expert: 'خبير',
      daysToExpiry: 'أيام حتى الانتهاء'
    },
    en: {
      title: 'Professional Certifications',
      description: 'Manage and track employee professional certifications',
      newCertification: 'New Certification',
      search: 'Search certifications...',
      certificationName: 'Certification Name',
      employee: 'Employee',
      issuingOrg: 'Issuing Organization',
      issueDate: 'Issue Date',
      expiryDate: 'Expiry Date',
      status: 'Status',
      level: 'Level',
      category: 'Category',
      certificateNumber: 'Certificate Number',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      download: 'Download',
      verify: 'Verify',
      totalCertifications: 'Total Certifications',
      activeCertifications: 'Active Certifications',
      expiringCertifications: 'Expiring Soon',
      active: 'Active',
      expired: 'Expired',
      pending: 'Pending',
      revoked: 'Revoked',
      basic: 'Basic',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      expert: 'Expert',
      daysToExpiry: 'Days to Expiry'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'expired': return 'bg-red-500/20 text-red-300 border-red-500/30'
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'revoked': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'basic': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'advanced': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'expert': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getDaysToExpiry = (expiryDate: string) => {
    const today = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const filteredCertifications = certifications.filter(cert => {
    const matchesSearch = cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.nameAr.includes(searchTerm) ||
                         cert.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cert.employeeAr.includes(searchTerm) ||
                         cert.certificateNumber.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || cert.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || cert.category === categoryFilter
    return matchesSearch && matchesStatus && matchesCategory
  })

  const activeCertifications = certifications.filter(cert => cert.status === 'active').length
  const expiringCertifications = certifications.filter(cert => {
    const daysToExpiry = getDaysToExpiry(cert.expiryDate)
    return cert.status === 'active' && daysToExpiry <= 90 && daysToExpiry > 0
  }).length

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newCertification}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalCertifications}</p>
                  <p className="text-2xl font-bold text-white">{certifications.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <Award className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeCertifications}</p>
                  <p className="text-2xl font-bold text-white">{activeCertifications}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.expiringCertifications}</p>
                  <p className="text-2xl font-bold text-white">{expiringCertifications}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <Clock className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="active">{t.active}</option>
                <option value="expired">{t.expired}</option>
                <option value="pending">{t.pending}</option>
                <option value="revoked">{t.revoked}</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الفئات</option>
                <option value="Management">الإدارة</option>
                <option value="Finance">المالية</option>
                <option value="Marketing">التسويق</option>
                <option value="Quality">الجودة</option>
                <option value="Technology">التكنولوجيا</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Certifications Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.certificationName}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.employee}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.issuingOrg}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.level}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.expiryDate}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCertifications.map(((cert) => {
                    const daysToExpiry = getDaysToExpiry(cert.expiryDate)
                    const isExpiringSoon = daysToExpiry <= 90 && daysToExpiry > 0

                    return (
                      <tr key={cert.id} className="border-b border-white/5 hover:bg-white/5">
                        <td className="p-4">
                          <div>
                            <span className="text-white font-medium">
                              {language === 'ar' ? cert.nameAr : cert.name}
                            </span>
                            <div className="text-xs text-white/60 mt-1">
                              {cert.certificateNumber}
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div>
                            <span className="text-white">{language === 'ar' ? cert.employeeAr : cert.employee}</span>
                            <div className="text-xs text-white/60">{cert.employeeId}</div>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className="text-white/70">
                            {language === 'ar' ? cert.issuingOrganizationAr : cert.issuingOrganization}
                          </span>
                        </td>
                        <td className="p-4">
                          <Badge className={`${getStatusColor(cert.status)} border`}>
                            {t[cert.status as keyof typeof t]}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <Badge className={`${getLevelColor(cert.level)} border`}>
                            {t[cert.level as keyof typeof t]}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <div>
                            <span className="text-white/70">{cert.expiryDate}</span>
                            {isExpiringSoon && (
                              <div className="text-xs text-orange-400 mt-1">
                                {daysToExpiry} {t.daysToExpiry}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex gap-2">
                            <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
