import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  GraduationCap,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Users,
  Clock,
  Calendar,
  BookOpen,
  Award,
  TrendingUp
} from 'lucide-react'

interface TrainingProgramsProps {
  language: 'ar' | 'en'
}

interface TrainingProgram {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  instructor: string
  instructorAr: string
  duration: number // in hours
  startDate: string
  endDate: string
  maxParticipants: number
  enrolledParticipants: number
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  category: string
  categoryAr: string
  level: 'beginner' | 'intermediate' | 'advanced'
  cost: number
  location: string
  locationAr: string
  isOnline: boolean
}

const mockPrograms: TrainingProgram[] = [
  {
    id: '1',
    title: 'Digital Marketing Fundamentals',
    titleAr: 'أساسيات التسويق الرقمي',
    description: 'Learn the basics of digital marketing including SEO, social media, and content marketing',
    descriptionAr: 'تعلم أساسيات التسويق الرقمي بما في ذلك تحسين محركات البحث ووسائل التواصل الاجتماعي والتسويق بالمحتوى',
    instructor: 'Sarah Ahmed',
    instructorAr: 'سارة أحمد',
    duration: 40,
    startDate: '2024-02-01',
    endDate: '2024-02-15',
    maxParticipants: 25,
    enrolledParticipants: 18,
    status: 'upcoming',
    category: 'Marketing',
    categoryAr: 'التسويق',
    level: 'beginner',
    cost: 2500,
    location: 'Training Room A',
    locationAr: 'قاعة التدريب أ',
    isOnline: false
  },
  {
    id: '2',
    title: 'Advanced Project Management',
    titleAr: 'إدارة المشاريع المتقدمة',
    description: 'Advanced techniques in project management using Agile and Scrum methodologies',
    descriptionAr: 'تقنيات متقدمة في إدارة المشاريع باستخدام منهجيات أجايل وسكرم',
    instructor: 'Mohammed Hassan',
    instructorAr: 'محمد حسن',
    duration: 60,
    startDate: '2024-01-20',
    endDate: '2024-02-10',
    maxParticipants: 20,
    enrolledParticipants: 20,
    status: 'ongoing',
    category: 'Management',
    categoryAr: 'الإدارة',
    level: 'advanced',
    cost: 4000,
    location: 'Online',
    locationAr: 'عبر الإنترنت',
    isOnline: true
  },
  {
    id: '3',
    title: 'Financial Analysis & Reporting',
    titleAr: 'التحليل المالي والتقارير',
    description: 'Comprehensive training on financial analysis, budgeting, and reporting',
    descriptionAr: 'تدريب شامل على التحليل المالي والميزانية والتقارير',
    instructor: 'Fatima Al-Zahra',
    instructorAr: 'فاطمة الزهراء',
    duration: 50,
    startDate: '2024-01-01',
    endDate: '2024-01-15',
    maxParticipants: 15,
    enrolledParticipants: 15,
    status: 'completed',
    category: 'Finance',
    categoryAr: 'المالية',
    level: 'intermediate',
    cost: 3500,
    location: 'Training Room B',
    locationAr: 'قاعة التدريب ب',
    isOnline: false
  }
]

export default function TrainingPrograms({ language }: TrainingProgramsProps) {
  const [programs, setPrograms] = useState<TrainingProgram[]>(mockPrograms)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'برامج التدريب',
      description: 'إدارة ومتابعة برامج التدريب والتطوير',
      newProgram: 'برنامج جديد',
      search: 'البحث في البرامج...',
      filter: 'تصفية',
      programTitle: 'عنوان البرنامج',
      instructor: 'المدرب',
      duration: 'المدة',
      participants: 'المشاركون',
      status: 'الحالة',
      startDate: 'تاريخ البداية',
      level: 'المستوى',
      cost: 'التكلفة',
      location: 'المكان',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      totalPrograms: 'إجمالي البرامج',
      totalParticipants: 'إجمالي المشاركين',
      avgCompletion: 'متوسط الإنجاز',
      upcoming: 'قادم',
      ongoing: 'جاري',
      completed: 'مكتمل',
      cancelled: 'ملغي',
      beginner: 'مبتدئ',
      intermediate: 'متوسط',
      advanced: 'متقدم',
      hours: 'ساعة',
      online: 'عبر الإنترنت',
      offline: 'حضوري'
    },
    en: {
      title: 'Training Programs',
      description: 'Manage and track training and development programs',
      newProgram: 'New Program',
      search: 'Search programs...',
      filter: 'Filter',
      programTitle: 'Program Title',
      instructor: 'Instructor',
      duration: 'Duration',
      participants: 'Participants',
      status: 'Status',
      startDate: 'Start Date',
      level: 'Level',
      cost: 'Cost',
      location: 'Location',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      totalPrograms: 'Total Programs',
      totalParticipants: 'Total Participants',
      avgCompletion: 'Avg Completion',
      upcoming: 'Upcoming',
      ongoing: 'Ongoing',
      completed: 'Completed',
      cancelled: 'Cancelled',
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      hours: 'Hours',
      online: 'Online',
      offline: 'Offline'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'ongoing': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'completed': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'cancelled': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'intermediate': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'advanced': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const filteredPrograms = programs.filter(program => {
    const matchesSearch = program.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         program.titleAr.includes(searchTerm) ||
                         program.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         program.instructorAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || program.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || program.category === categoryFilter
    return matchesSearch && matchesStatus && matchesCategory
  })

  const totalParticipants = programs.reduce(((sum, program) => sum + program.enrolledParticipants, 0)
  const completedPrograms = programs.filter(p => p.status === 'completed').length
  const avgCompletion = programs.length > 0 ? (completedPrograms / programs.length) * 100 : 0

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newProgram}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalPrograms}</p>
                  <p className="text-2xl font-bold text-white">{programs.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <GraduationCap className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalParticipants}</p>
                  <p className="text-2xl font-bold text-white">{totalParticipants}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <Users className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgCompletion}</p>
                  <p className="text-2xl font-bold text-white">{avgCompletion.toFixed(1)}%</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="upcoming">{t.upcoming}</option>
                <option value="ongoing">{t.ongoing}</option>
                <option value="completed">{t.completed}</option>
                <option value="cancelled">{t.cancelled}</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الفئات</option>
                <option value="Marketing">التسويق</option>
                <option value="Management">الإدارة</option>
                <option value="Finance">المالية</option>
                <option value="Technology">التكنولوجيا</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Programs Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.programTitle}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.instructor}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.duration}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.participants}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.level}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.startDate}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPrograms.map(((program) => (
                    <tr key={program.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <div>
                          <span className="text-white font-medium">
                            {language === 'ar' ? program.titleAr : program.title}
                          </span>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {language === 'ar' ? program.categoryAr : program.category}
                            </Badge>
                            {program.isOnline && (
                              <Badge variant="outline" className="text-xs bg-blue-500/20 text-blue-300">
                                {t.online}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? program.instructorAr : program.instructor}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{program.duration} {t.hours}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{program.enrolledParticipants}/{program.maxParticipants}</span>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(program.status)} border`}>
                          {t[program.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getLevelColor(program.level)} border`}>
                          {t[program.level as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <span className="text-white/70">{program.startDate}</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
