import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import {
  FileText,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Download,
  Send,
  Copy,
  Calculator
} from 'lucide-react'

interface QuotationsProps {
  language: 'ar' | 'en'
}

interface Quotation {
  id: string
  quotationNumber: string
  customer: string
  customerAr: string
  amount: number
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired'
  validUntil: string
  createdDate: string
  items: number
  discount: number
}

const mockQuotations: Quotation[] = [
  {
    id: '1',
    quotationNumber: 'QT-2024-001',
    customer: 'ABC Company',
    customerAr: 'شركة أي بي سي',
    amount: 25000,
    status: 'sent',
    validUntil: '2024-02-15',
    createdDate: '2024-01-15',
    items: 7,
    discount: 5
  },
  {
    id: '2',
    quotationNumber: 'QT-2024-002',
    customer: 'XYZ Corporation',
    customerAr: 'مؤسسة إكس واي زد',
    amount: 18500,
    status: 'accepted',
    validUntil: '2024-02-20',
    createdDate: '2024-01-16',
    items: 4,
    discount: 10
  },
  {
    id: '3',
    quotationNumber: 'QT-2024-003',
    customer: 'Tech Solutions Ltd',
    customerAr: 'شركة الحلول التقنية المحدودة',
    amount: 32000,
    status: 'draft',
    validUntil: '2024-02-25',
    createdDate: '2024-01-17',
    items: 12,
    discount: 0
  }
]

export default function Quotations({ language }: QuotationsProps) {
  const [quotations, setQuotations] = useState<Quotation[]>(mockQuotations)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const text = {
    ar: {
      title: 'عروض الأسعار',
      description: 'إدارة ومتابعة عروض الأسعار',
      newQuotation: 'عرض سعر جديد',
      search: 'البحث في عروض الأسعار...',
      quotationNumber: 'رقم العرض',
      customer: 'العميل',
      amount: 'المبلغ',
      status: 'الحالة',
      validUntil: 'صالح حتى',
      createdDate: 'تاريخ الإنشاء',
      items: 'العناصر',
      discount: 'الخصم',
      actions: 'الإجراءات',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      send: 'إرسال',
      copy: 'نسخ',
      download: 'تحميل',
      totalQuotations: 'إجمالي العروض',
      totalValue: 'إجمالي القيمة',
      acceptanceRate: 'معدل القبول',
      draft: 'مسودة',
      sent: 'مرسل',
      viewed: 'تم العرض',
      accepted: 'مقبول',
      rejected: 'مرفوض',
      expired: 'منتهي الصلاحية'
    },
    en: {
      title: 'Quotations',
      description: 'Manage and track quotations',
      newQuotation: 'New Quotation',
      search: 'Search quotations...',
      quotationNumber: 'Quotation Number',
      customer: 'Customer',
      amount: 'Amount',
      status: 'Status',
      validUntil: 'Valid Until',
      createdDate: 'Created Date',
      items: 'Items',
      discount: 'Discount',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      send: 'Send',
      copy: 'Copy',
      download: 'Download',
      totalQuotations: 'Total Quotations',
      totalValue: 'Total Value',
      acceptanceRate: 'Acceptance Rate',
      draft: 'Draft',
      sent: 'Sent',
      viewed: 'Viewed',
      accepted: 'Accepted',
      rejected: 'Rejected',
      expired: 'Expired'
    }
  }

  const t = text[language]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      case 'sent': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'viewed': return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'accepted': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'rejected': return 'bg-red-500/20 text-red-300 border-red-500/30'
      case 'expired': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const filteredQuotations = quotations.filter(quotation => {
    const matchesSearch = quotation.quotationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.customerAr.includes(searchTerm)
    const matchesStatus = statusFilter === 'all' || quotation.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const totalValue = quotations.reduce(((sum, quotation) => sum + quotation.amount, 0)
  const acceptedQuotations = quotations.filter(q => q.status === 'accepted').length
  const acceptanceRate = quotations.length > 0 ? (acceptedQuotations / quotations.length) * 100 : 0

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <div className="flex gap-3">
            <Button className="glass-button">
              <Download className="w-4 h-4 mr-2" />
              {t.download}
            </Button>
            <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              {t.newQuotation}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalQuotations}</p>
                  <p className="text-2xl font-bold text-white">{quotations.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalValue}</p>
                  <p className="text-2xl font-bold text-white">{totalValue.toLocaleString()} ر.س</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <Calculator className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.acceptanceRate}</p>
                  <p className="text-2xl font-bold text-white">{acceptanceRate.toFixed(1)}%</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="modern-card">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-4 h-4" />
                  <Input
                    placeholder={t.search}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 glass-input"
                  />
                </div>
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="glass-input px-3 py-2 rounded-lg"
              >
                <option value="all">جميع الحالات</option>
                <option value="draft">{t.draft}</option>
                <option value="sent">{t.sent}</option>
                <option value="viewed">{t.viewed}</option>
                <option value="accepted">{t.accepted}</option>
                <option value="rejected">{t.rejected}</option>
                <option value="expired">{t.expired}</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Quotations Table */}
        <Card className="modern-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10">
                    <th className="text-right p-4 text-white/70 font-medium">{t.quotationNumber}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.customer}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.amount}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.status}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.validUntil}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.items}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.discount}</th>
                    <th className="text-right p-4 text-white/70 font-medium">{t.actions}</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredQuotations.map(((quotation) => (
                    <tr key={quotation.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="p-4">
                        <span className="text-white font-medium">{quotation.quotationNumber}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{language === 'ar' ? quotation.customerAr : quotation.customer}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{quotation.amount.toLocaleString()} ر.س</span>
                      </td>
                      <td className="p-4">
                        <Badge className={`${getStatusColor(quotation.status)} border`}>
                          {t[quotation.status as keyof typeof t]}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <span className="text-white/70">{quotation.validUntil}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{quotation.items}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-white">{quotation.discount}%</span>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-white/70 hover:text-white">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                            <Send className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-green-400 hover:text-green-300">
                            <Copy className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
    </div>
  )
}
