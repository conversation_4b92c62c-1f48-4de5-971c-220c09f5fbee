import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import {
  Target,
  Plus,
  Eye,
  Edit,
  ArrowRight,
  TrendingUp,
  DollarSign,
  Users,
  Calendar
} from 'lucide-react'

interface SalesPipelineProps {
  language: 'ar' | 'en'
}

interface Lead {
  id: string
  name: string
  nameAr: string
  company: string
  companyAr: string
  value: number
  stage: 'lead' | 'qualified' | 'proposal' | 'negotiation' | 'closed-won' | 'closed-lost'
  probability: number
  expectedCloseDate: string
  lastActivity: string
  source: string
}

const mockLeads: Lead[] = [
  {
    id: '1',
    name: '<PERSON>',
    nameAr: 'أحمد الراشد',
    company: 'Tech Solutions',
    companyAr: 'شركة الحلول التقنية',
    value: 50000,
    stage: 'proposal',
    probability: 75,
    expectedCloseDate: '2024-02-15',
    lastActivity: '2024-01-18',
    source: 'Website'
  },
  {
    id: '2',
    name: '<PERSON>',
    nameAr: 'سارة جونسون',
    company: 'Global Corp',
    companyAr: 'الشركة العالمية',
    value: 35000,
    stage: 'negotiation',
    probability: 85,
    expectedCloseDate: '2024-02-10',
    lastActivity: '2024-01-19',
    source: 'Referral'
  },
  {
    id: '3',
    name: 'Mohammed Hassan',
    nameAr: 'محمد حسن',
    company: 'Innovation Hub',
    companyAr: 'مركز الابتكار',
    value: 75000,
    stage: 'qualified',
    probability: 60,
    expectedCloseDate: '2024-03-01',
    lastActivity: '2024-01-17',
    source: 'Cold Call'
  }
]

const stages = [
  { id: 'lead', nameAr: 'عميل محتمل', nameEn: 'Lead', color: 'bg-gray-500' },
  { id: 'qualified', nameAr: 'مؤهل', nameEn: 'Qualified', color: 'bg-blue-500' },
  { id: 'proposal', nameAr: 'عرض', nameEn: 'Proposal', color: 'bg-purple-500' },
  { id: 'negotiation', nameAr: 'تفاوض', nameEn: 'Negotiation', color: 'bg-orange-500' },
  { id: 'closed-won', nameAr: 'مغلق - فوز', nameEn: 'Closed Won', color: 'bg-green-500' },
  { id: 'closed-lost', nameAr: 'مغلق - خسارة', nameEn: 'Closed Lost', color: 'bg-red-500' }
]

export default function SalesPipeline({ language }: SalesPipelineProps) {
  const [leads, setLeads] = useState<Lead[]>(mockLeads)

  const text = {
    ar: {
      title: 'مسار المبيعات',
      description: 'متابعة وإدارة مسار المبيعات والعملاء المحتملين',
      newLead: 'عميل محتمل جديد',
      totalValue: 'إجمالي القيمة',
      totalLeads: 'إجمالي العملاء المحتملين',
      avgDealSize: 'متوسط حجم الصفقة',
      conversionRate: 'معدل التحويل',
      probability: 'الاحتمالية',
      expectedClose: 'الإغلاق المتوقع',
      lastActivity: 'آخر نشاط',
      source: 'المصدر',
      view: 'عرض',
      edit: 'تعديل',
      move: 'نقل'
    },
    en: {
      title: 'Sales Pipeline',
      description: 'Track and manage sales pipeline and leads',
      newLead: 'New Lead',
      totalValue: 'Total Value',
      totalLeads: 'Total Leads',
      avgDealSize: 'Avg Deal Size',
      conversionRate: 'Conversion Rate',
      probability: 'Probability',
      expectedClose: 'Expected Close',
      lastActivity: 'Last Activity',
      source: 'Source',
      view: 'View',
      edit: 'Edit',
      move: 'Move'
    }
  }

  const t = text[language]

  const getLeadsByStage = (stageId: string) => {
    return leads.filter(lead => lead.stage === stageId)
  }

  const getTotalValue = () => {
    return leads.reduce(((sum, lead) => sum + lead.value, 0)
  }

  const getAvgDealSize = () => {
    return leads.length > 0 ? getTotalValue() / leads.length : 0
  }

  const getConversionRate = () => {
    const closedWon = leads.filter(lead => lead.stage === 'closed-won').length
    return leads.length > 0 ? (closedWon / leads.length) * 100 : 0
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
            <p className="text-white/70">{t.description}</p>
          </div>
          <Button className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.newLead}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalValue}</p>
                  <p className="text-2xl font-bold text-white">{getTotalValue().toLocaleString()} ر.س</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.totalLeads}</p>
                  <p className="text-2xl font-bold text-white">{leads.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center">
                  <Users className="w-6 h-6 text-secondary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.avgDealSize}</p>
                  <p className="text-2xl font-bold text-white">{getAvgDealSize().toLocaleString()} ر.س</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-accent/20 flex items-center justify-center">
                  <Target className="w-6 h-6 text-accent" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="modern-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.conversionRate}</p>
                  <p className="text-2xl font-bold text-white">{getConversionRate().toFixed(1)}%</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-muted/20 flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pipeline Stages */}
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
          {stages.map(((stage) => {
            const stageLeads = getLeadsByStage(stage.id)
            const stageValue = stageLeads.reduce(((sum, lead) => sum + lead.value, 0)

            return (
              <Card key={stage.id} className="modern-card">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white text-sm">
                      {language === 'ar' ? stage.nameAr : stage.nameEn}
                    </CardTitle>
                    <div className={`w-3 h-3 rounded-full ${stage.color}`}></div>
                  </div>
                  <div className="text-white/70 text-xs">
                    {stageLeads.length} عميل • {stageValue.toLocaleString()} ر.س
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  {stageLeads.map(((lead) => (
                    <div key={lead.id} className="p-3 rounded-lg bg-white/5 border border-white/10">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="text-white text-sm font-medium">
                            {language === 'ar' ? lead.nameAr : lead.name}
                          </h4>
                          <p className="text-white/60 text-xs">
                            {language === 'ar' ? lead.companyAr : lead.company}
                          </p>
                        </div>
                        <div className="flex gap-1">
                          <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70 hover:text-white">
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-white/70 hover:text-white">
                            <Edit className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between text-white/70">
                          <span>القيمة:</span>
                          <span>{lead.value.toLocaleString()} ر.س</span>
                        </div>
                        <div className="flex justify-between text-white/70">
                          <span>{t.probability}:</span>
                          <span>{lead.probability}%</span>
                        </div>
                        <div className="flex justify-between text-white/70">
                          <span>{t.expectedClose}:</span>
                          <span>{lead.expectedCloseDate}</span>
                        </div>
                      </div>

                      <div className="mt-2 pt-2 border-t border-white/10">
                        <div className="flex justify-between items-center text-xs text-white/60">
                          <span>{t.source}: {lead.source}</span>
                          <span>{lead.lastActivity}</span>
                        </div>
                      </div>
                    </div>
                  ))}

                  {stageLeads.length === 0 && (
                    <div className="text-center py-8 text-white/50 text-sm">
                      لا توجد عملاء محتملين في هذه المرحلة
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
    </div>
  )
}
