import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON> 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>,
  AlertTriangle,
  XCircle,
  Users,
  Activity,
  FileText,
  Eye,
  Settings
} from 'lucide-react';
// Language is passed as prop, not from context

interface SecurityStats {
  total_users: number;
  mfa_enabled_users: number;
  mfa_adoption_rate: number;
  locked_accounts: number;
  high_security_users: number;
  critical_security_users: number;
  recent_failed_logins_24h: number;
}

interface IncidentStats {
  total_incidents: number;
  open_incidents: number;
  critical_incidents: number;
  resolved_incidents: number;
  recent_incidents_30d: number;
  average_resolution_hours: number;
  incident_type_breakdown: Array<{
    incident_type: string;
    count: number;
  }>;
}

interface AlertStats {
  total_alerts: number;
  open_alerts: number;
  critical_alerts: number;
  high_alerts: number;
  escalated_alerts: number;
  resolved_alerts: number;
  recent_alerts_24h: number;
  average_response_time_minutes: number;
  average_resolution_time_hours: number;
}

interface ComplianceStats {
  total_frameworks: number;
  active_frameworks: number;
  average_compliance_score: number;
  overdue_assessments: number;
  top_frameworks: Array<{
    name: string;
    framework_type: string;
    compliance_score: number;
  }>;
}

interface RecentIncident {
  id: number;
  incident_id: string;
  title: string;
  incident_type: string;
  severity: string;
  status: string;
  detected_at: string;
  assigned_to_name: string;
}

interface RecentAlert {
  id: number;
  alert_id: string;
  title: string;
  alert_type: string;
  severity: string;
  status: string;
  detected_at: string;
  risk_score: number;
}

interface SecurityDashboardProps {
  language: 'ar' | 'en';
}
const SecurityDashboard: React.FC<SecurityDashboardProps> = ({ language }) => {
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null);
  const [incidentStats, setIncidentStats] = useState<IncidentStats | null>(null);
  const [alertStats, setAlertStats] = useState<AlertStats | null>(null);
  const [complianceStats, setComplianceStats] = useState<ComplianceStats | null>(null);
  const [recentIncidents, setRecentIncidents] = useState<RecentIncident[]>([]);
  const [recentAlerts, setRecentAlerts] = useState<RecentAlert[]>([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    fetchSecurityData();
  }, []);
  const fetchSecurityData: any = async () => {
    try {
      const [
        securityResponse,
        incidentResponse,
        alertResponse,
        complianceResponse,
        recentIncidentsResponse,
        recentAlertsResponse
      ] = await Promise.all([
        fetch('/api/user-security-profiles/security_stats/'),
        fetch('/api/security-incidents/incident_stats/'),
        fetch('/api/security-alerts/alert_stats/'),
        fetch('/api/compliance-frameworks/compliance_dashboard/'),
        fetch('/api/security-incidents/?ordering=-detected_at&page_size=5'),
        fetch('/api/security-alerts/?ordering=-detected_at&page_size=5')
      ]);

      if (securityResponse.ok) {
        const data: any = await securityResponse.json();
        setSecurityStats(data);
      }

      if (incidentResponse.ok) {
        const data: any = await incidentResponse.json();
        setIncidentStats(data);
      }

      if (alertResponse.ok) {
        const data: any = await alertResponse.json();
        setAlertStats(data);
      }

      if (complianceResponse.ok) {
        const data: any = await complianceResponse.json();
        setComplianceStats(data);
      }

      if (recentIncidentsResponse.ok) {
        const data: any = await recentIncidentsResponse.json();
        setRecentIncidents(data.results || data);
      }

      if (recentAlertsResponse.ok) {
        const data: any = await recentAlertsResponse.json();
        setRecentAlerts(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching security data:', error);
    } finally {
      setLoading(false);
    }
  };
  const getSeverityColor: any = (severity: string): void => {
    const colors = {
      'CRITICAL': 'bg-red-100 text-red-800',
      'HIGH': 'bg-orange-100 text-orange-800',
      'MEDIUM': 'bg-yellow-100 text-yellow-800',
      'LOW': 'bg-blue-100 text-blue-800',
      'INFO': 'bg-gray-100 text-gray-800'
    };
    return colors[severity as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };
  const getStatusColor: any = (status: string): void => {
    const colors = {
      'OPEN': 'bg-red-100 text-red-800',
      'INVESTIGATING': 'bg-yellow-100 text-yellow-800',
      'CONTAINED': 'bg-blue-100 text-blue-800',
      'RESOLVED': 'bg-green-100 text-green-800',
      'CLOSED': 'bg-gray-100 text-gray-800',
      'ACKNOWLEDGED': 'bg-blue-100 text-blue-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };
  const formatDate: any = (dateString: string): string => {
    return new DatedateString.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة تحكم الأمان' : 'Security Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'مراقبة الأمان والامتثال والحوادث الأمنية'
              : 'Monitor security, compliance, and security incidents'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'الإعدادات' : 'Settings'}
          </Button>
          <Button onClick={fetchSecurityData}>
            <Activity className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Security Overview */}
      {securityStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'}
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{securityStats.total_users}</div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'مستخدم نشط' : 'active users'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'معدل اعتماد MFA' : 'MFA Adoption'}
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {securityStats.mfa_adoption_rate.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {securityStats.mfa_enabled_users} {language === 'ar' ? 'من' : 'of'} {securityStats.total_users}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'الحسابات المقفلة' : 'Locked Accounts'}
              </CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{securityStats.locked_accounts}</div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'تتطلب إلغاء القفل' : 'require unlock'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'محاولات تسجيل دخول فاشلة' : 'Failed Logins (24h)'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {securityStats.recent_failed_logins_24h}
              </div>
              <p className="text-xs text-muted-foreground">
                {language === 'ar' ? 'آخر 24 ساعة' : 'last 24 hours'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Incidents and Alerts Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Security Incidents */}
        {incidentStats && (<Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                {language === 'ar' ? 'الحوادث الأمنية' : 'Security Incidents'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{incidentStats.open_incidents}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'مفتوحة' : 'Open'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{incidentStats.critical_incidents}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'حرجة' : 'Critical'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{incidentStats.resolved_incidents}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'محلولة' : 'Resolved'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{incidentStats.average_resolution_hours.toFixed(1)}h</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'متوسط الحل' : 'Avg Resolution'}
                  </div>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                {incidentStats.recent_incidents_30d} {language === 'ar' ? 'حوادث جديدة آخر 30 يوم' : 'new incidents in last 30 days'}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Security Alerts */}
        {alertStats && (<Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {language === 'ar' ? 'التنبيهات الأمنية' : 'Security Alerts'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{alertStats.open_alerts}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'مفتوحة' : 'Open'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{alertStats.critical_alerts}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'حرجة' : 'Critical'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{alertStats.resolved_alerts}</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'محلولة' : 'Resolved'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{alertStats.average_response_time_minutes.toFixed(1)}m</div>
                  <div className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'متوسط الاستجابة' : 'Avg Response'}
                  </div>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                {alertStats.recent_alerts_24h} {language === 'ar' ? 'تنبيهات جديدة آخر 24 ساعة' : 'new alerts in last 24 hours'}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Compliance Overview */}
      {complianceStats && (<Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {language === 'ar' ? 'نظرة عامة على الامتثال' : 'Compliance Overview'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold">{complianceStats.total_frameworks}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'أطر العمل' : 'Frameworks'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{complianceStats.active_frameworks}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'نشطة' : 'Active'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {complianceStats.average_compliance_score.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'متوسط الامتثال' : 'Avg Compliance'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{complianceStats.overdue_assessments}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'تقييمات متأخرة' : 'Overdue Assessments'}
                </div>
              </div>
            </div>

            {complianceStats.top_frameworks.length > 0 && (<div>
                <h4 className="font-medium mb-3">
                  {language === 'ar' ? 'أفضل أطر الامتثال' : 'Top Compliance Frameworks'}
                </h4>
                <div className="space-y-2">
                  {complianceStats.top_frameworks.map(((framework, index) => (<div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div>
                        <div className="font-medium">{framework.name}</div>
                        <div className="text-sm text-muted-foreground">{framework.framework_type}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">{framework.compliance_score.toFixed(1)}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Incidents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                {language === 'ar' ? 'الحوادث الأخيرة' : 'Recent Incidents'}
              </span>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'عرض الكل' : 'View All'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentIncidents.map(((incident) => (<div key={incident.id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{incident.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {incident.incident_id} • {formatDate(incident.detected_at)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getSeverityColor(incident.severity)}>
                      {incident.severity}
                    </Badge>
                    <Badge className={getStatusColor(incident.status)}>
                      {incident.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                {language === 'ar' ? 'التنبيهات الأخيرة' : 'Recent Alerts'}
              </span>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'عرض الكل' : 'View All'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentAlerts.map(((alert) => (<div key={alert.id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <div className="font-medium">{alert.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {alert.alert_id} • {formatDate(alert.detected_at)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm font-medium">
                      {language === 'ar' ? 'المخاطر:' : 'Risk:'} {alert.risk_score}
                    </div>
                    <Badge className={getSeverityColor(alert.severity)}>
                      {alert.severity}
                    </Badge>
                    <Badge className={getStatusColor(alert.status)}>
                      {alert.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SecurityDashboard;
